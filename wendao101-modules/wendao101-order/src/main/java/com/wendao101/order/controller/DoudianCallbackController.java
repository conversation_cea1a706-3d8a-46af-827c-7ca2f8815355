package com.wendao101.order.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bytedance.openapi.TradeSystemSign;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailRequest;
import com.doudian.open.api.afterSale_Detail.AfterSaleDetailResponse;
import com.doudian.open.api.afterSale_Detail.data.AfterSaleDetailData;
import com.doudian.open.api.afterSale_Detail.param.AfterSaleDetailParam;
import com.doudian.open.api.coupons_syncV2.CouponsSyncV2Request;
import com.doudian.open.api.coupons_syncV2.CouponsSyncV2Response;
import com.doudian.open.api.coupons_syncV2.param.CertListItem;
import com.doudian.open.api.coupons_syncV2.param.CouponsSyncV2Param;
import com.doudian.open.api.material_queryMaterialDetail.data.MaterialInfo;
import com.doudian.open.api.material_queryMaterialDetail.data.MaterialQueryMaterialDetailData;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailRequest;
import com.doudian.open.api.order_orderDetail.OrderOrderDetailResponse;
import com.doudian.open.api.order_orderDetail.data.OrderOrderDetailData;
import com.doudian.open.api.order_orderDetail.data.SkuOrderListItem;
import com.doudian.open.api.order_orderDetail.param.OrderOrderDetailParam;
import com.doudian.open.api.product_detail.ProductDetailRequest;
import com.doudian.open.api.product_detail.ProductDetailResponse;
import com.doudian.open.api.product_detail.data.ProductDetailData;
import com.doudian.open.api.product_detail.data.SpecPricesItem;
import com.doudian.open.api.product_detail.param.ProductDetailParam;
import com.doudian.open.api.sms_send.SmsSendRequest;
import com.doudian.open.api.sms_send.SmsSendResponse;
import com.doudian.open.api.sms_send.param.SmsSendParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.core.msg.MsgParam;
import com.doudian.open.msg.material_auditResultForShop.MaterialAuditResultForShopRequest;
import com.doudian.open.msg.material_auditResultForShop.param.MaterialAuditResultForShopParam;
import com.doudian.open.msg.product_InfoChange.ProductInfoChangeRequest;
import com.doudian.open.msg.product_InfoChange.param.ProductInfoChangeParam;
import com.doudian.open.msg.product_InfoChange.param.Status;
import com.doudian.open.msg.product_change.ProductChangeRequest;
import com.doudian.open.msg.product_change.param.ProductChangeParam;
import com.doudian.open.msg.refund_RefundClosed.RefundRefundClosedRequest;
import com.doudian.open.msg.refund_RefundClosed.param.RefundRefundClosedParam;
import com.doudian.open.msg.refund_RefundCreated.RefundRefundCreatedRequest;
import com.doudian.open.msg.refund_RefundCreated.param.RefundRefundCreatedParam;
import com.doudian.open.msg.refund_RefundSuccess.RefundRefundSuccessRequest;
import com.doudian.open.msg.refund_RefundSuccess.param.RefundRefundSuccessParam;
import com.doudian.open.msg.trade_TradeCreate.TradeTradeCreateRequest;
import com.doudian.open.msg.trade_TradeCreate.param.TradeTradeCreateParam;
import com.doudian.open.msg.trade_TradePaid.TradeTradePaidRequest;
import com.doudian.open.msg.trade_TradePaid.param.TradeTradePaidParam;
import com.doudian.open.msg.trade_TradeSellerShip.TradeTradeSellerShipRequest;
import com.doudian.open.msg.trade_TradeSellerShip.param.TradeTradeSellerShipParam;
import com.github.binarywang.wxpay.bean.request.BaseWxPayRequest;
import com.wendao101.common.core.doudian.AfterSaleReasons;
import com.wendao101.common.core.doudian.AfterSaleReasonsNeedHandle;
import com.wendao101.common.core.doudian.DoudianOrderSkuDTO;
import com.wendao101.common.core.douyin.generatelink.GenerateUrlLinkDTO;
import com.wendao101.common.core.douyin.generatelink.GenerateUrlLinkResult;
import com.wendao101.common.core.kspaydto.CreateWendaoRefundDTO;
import com.wendao101.common.core.ktdto.GenerateUrlLinkRequestExt;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.wxmessage.RefundApplyMessage;
import com.wendao101.common.core.xhs.common.SkuOrderInfo;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.api.config.DouyinConfig;
import com.wendao101.douyin.api.feign.WxSendMessageService;
import com.wendao101.order.config.OrderAppConfig;
import com.wendao101.order.constants.Appids;
import com.wendao101.order.domain.*;
import com.wendao101.order.dto.PromoterAuthorizedAccountDTO;
import com.wendao101.order.feign.WxMaLinkService;
import com.wendao101.order.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 课程评论点赞Controller
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@Slf4j
@RestController
@RequestMapping("/wxkuaishou_pay_controller")
public class DoudianCallbackController extends BaseController {
    private static final String REFUND_REASON_KEY_PREFIX = "REFUND_REASON_KEY_PREFIX";
    private static final String doudian_material_upload_prefix = "doudian_material_upload_prefix:";
    private static final String doudian_sms_send_param_prefix = "doudian_sms_send_param_prefix:";
    private static final String doudian_sms_send_sms_id_order_id_prefix = "doudian_sms_send_sms_id_order_id_prefix:";
    private static final String doudian_order_coupons_redis_prefix = "doudian_order_coupons_redis_prefix:";
    private static final String doudian_resend_sms_redis_prefix = "doudian_resend_sms_redis_prefix:";
    private static final String doudian_order_coupons_redis_prefix_shop_id = "doudian_order_coupons_redis_prefix_shop_id:";
    private static final String doudian_afterSale_shopId_redis_prefix = "doudian_afterSale_shopId_redis_prefix:";
    @Autowired
    private IDoudianShopConfigService doudianShopConfigService;
    @Autowired
    private ICourseOrderService courseOrderService;
    @Autowired
    private ICourseAuditService courseAuditService;
    @Autowired
    private IWendaoUserService wendaoUserService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IPromoterCourseService promoterCourseService;
    @Autowired
    private IPromoterService promoterService;
    @Autowired
    private ICourseRefundService courseRefundService;
    @Autowired
    private IMessageNotificationService messageNotificationService;
    @Autowired
    private IFundIncomeService fundIncomeService;
    @Autowired
    private DouyinConfig dyConfig;
    @Autowired
    private WxMaLinkService wxMaLinkService;
    @Autowired
    private IDoudianCourseService doudianCourseService;
    @Autowired
    private DoudianPriveteOrderService doudianPriveteOrderService;
    @Autowired
    private WxSendMessageService wxSendMessageService;
    @Autowired
    private DdHexiaoWhitelistService ddHexiaoWhitelistService;
    @Autowired
    private WendaoSettlementService wendaoSettlementService;
    @Autowired
    private IShopGotowxWhitelistService shopGotowxWhitelistService;

    /**
     * 根据消息推送服务接入指南提供的信息，抖店开放平台对接收消息后的响应时间有严格要求。
     * 平台服务端响应的超时时间为2秒，建议开发者在接收到消息后立即返回成功的code（{"code":0,"msg":"success"}），
     * 再异步去处理自有的业务信息。如果超过2秒未返回成功code，
     * 系统会认为消息推送失败，并自动进行消息重推，最多重推3次，推送的时间间隔分别为30秒、5分钟和1小时。
     *
     * @param request
     * @return
     */
    @PostMapping("/doudian_callback")
    public AjaxResult doudianCallback(HttpServletRequest request) {
        //获取请求体
        String requestBody = null;
        try {
            requestBody = TradeSystemSign.getRequestBody(request);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        System.out.println("接收到的抖店信息:" + requestBody);
        String eventSign = request.getHeader("event-sign");
        String appId = request.getHeader("app-id");
        List<JSONObject> jsonObjects = JSON.parseArray(requestBody, JSONObject.class);
        for (JSONObject json : jsonObjects) {
            if (json.getString("tag").equals("137")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //素材上传状态变更消息
                processMaterialChange(appId, eventSign, JSON.toJSONString(list));
            }
            if (json.getString("tag").equals("400")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //商品变更消息
                processProductChange(appId, eventSign, JSON.toJSONString(list));
            }
            if (json.getString("tag").equals("417")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //商品变更消息
                processProductInfoChange(appId, eventSign, JSON.toJSONString(list));
            }
            if (json.getString("tag").equals("100")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //订单创建
                processCreateOrder(appId, eventSign, JSON.toJSONString(list));
            }
            if (json.getString("tag").equals("101")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //订单支付,当创建和支付同时来的时候需要延迟
                boolean processResult = processOrderPay(appId, eventSign, JSON.toJSONString(list));
                if (!processResult) {
                    //处理业务失败,等待抖店系统重发! 30秒后抖店会重发
                    return new AjaxResult(1, "fail");
                }
            }
            if (json.getString("tag").equals("102")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //发货消息
                //TradeTradeSellerShipParam
                processOrderShip(appId, eventSign, JSON.toJSONString(list));
            }
//            if (json.getString("tag").equals("103")) {
//                List<JSONObject> list = new ArrayList<>();
//                list.add(json);
//                //交易完成,结算金额到老师账户://TODO:
//            }
            if (json.getString("tag").equals("200")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //买家发起售后
                processOrderRefund(appId, eventSign, JSON.toJSONString(list));
            }
//            if (json.getString("tag").equals("201")) {
//                List<JSONObject> list = new ArrayList<>();
//                list.add(json);
//                //同意退款
//                processOrderRefundAgreed(appId, eventSign, JSON.toJSONString(list));
//            }
            if (json.getString("tag").equals("207")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //售后关闭
                processOrderRefundClosed(appId, eventSign, JSON.toJSONString(list));
            }
            if (json.getString("tag").equals("206")) {
                List<JSONObject> list = new ArrayList<>();
                list.add(json);
                //退款成功消息
                processRefundSucceed(appId, eventSign, JSON.toJSONString(list));
            }
//            if (json.getString("tag").equals("204")) {
//                List<JSONObject> list = new ArrayList<>();
//                list.add(json);
//                //拒绝退款消息
//                processRefundRefused(appId, eventSign, JSON.toJSONString(list));
//            }
//            if (json.getString("tag").equals("205")) {
//                List<JSONObject> list = new ArrayList<>();
//                list.add(json);
//                //拒绝退货申请消息
//                processReturnApplyRefused(appId, eventSign, JSON.toJSONString(list));
//            }
//            if (json.getString("tag").equals("105")) {
//                List<JSONObject> list = new ArrayList<>();
//                list.add(json);
//                //买家收货信息变更消息
//                processBuyerAddressChange(appId, eventSign, JSON.toJSONString(list));
//            }
        }
        //处理业务逻辑
        return new AjaxResult(0, "success");
    }

    private void processOrderShip(String appId, String eventSign, String requestBody) {
        TradeTradeSellerShipRequest tradeTradeSellerShipRequest = new TradeTradeSellerShipRequest();
        MsgParam param = tradeTradeSellerShipRequest.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<TradeTradeSellerShipParam>> list = tradeTradeSellerShipRequest.getRequestBody();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DoudianOpMsgParamRecord<TradeTradeSellerShipParam> item : list) {
            TradeTradeSellerShipParam data = item.getData();
            Long shopId = data.getShopId();
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(shopId);
            if (doudianShopConfig.getIsPublic() == 0) {
                continue;
            }
            //如果是实物订单,只执行主订单扣款5元,子订单不计算
            if (data.getOrderType() != null && data.getOrderType() == 0L) {
                String orderIdStr = String.valueOf(data.getPId());
                CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderIdStr);
                if (courseOrder == null) {
                    System.out.println("订单不存在，订单Id：" + orderIdStr);
                    //不存在!
                    continue;
                }
                // 扣除费用 插入一条扣款资金明细
                // 先查询订单是否已经有扣款记录,实物只扣除老师的金额
                FundIncome fundIncomeNew = new FundIncome();
                fundIncomeNew.setTeacherId(courseOrder.getTeacherId());
                fundIncomeNew.setOrderId(orderIdStr);
                fundIncomeNew.setIncomePlatform(11);
                List<FundIncome> fundIncomes = fundIncomeService.selectFundIncomeList(fundIncomeNew);
                if (CollectionUtils.isNotEmpty(fundIncomes)) {
                    FundIncome fundIncome = fundIncomes.get(0);
                    if (fundIncome.getRealIncomePrice() == null) {
                        fundIncome.setRealIncomePrice(BigDecimal.ZERO);
                    }
                    fundIncome.setLogisticsFee(new BigDecimal(5));
                    fundIncome.setRealIncomePrice(fundIncome.getRealIncomePrice().subtract(fundIncome.getLogisticsFee()));
                    fundIncomeService.updateFundIncome(fundIncome);
                }
            }
        }
    }

    private void processProductInfoChange(String appId, String eventSign, String requestBody) {
        ProductInfoChangeRequest productInfoChangeRequest = new ProductInfoChangeRequest();
        MsgParam param = productInfoChangeRequest.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<ProductInfoChangeParam>> list = productInfoChangeRequest.getRequestBody();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DoudianOpMsgParamRecord<ProductInfoChangeParam> item : list) {
            ProductInfoChangeParam data = item.getData();
            Long productId = data.getProductId();
            //先查出商品
            DoudianCourse doudianCourse = doudianCourseService.selectDoudianCourseByProductId(productId);
            if (doudianCourse == null) {
                continue;
            }
            Long shopId = data.getShopId();
            if (shopId != null && shopId > 0L) {
                DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(shopId);
                if (doudianShopConfig.getIsPublic() != null && doudianShopConfig.getIsPublic() == 1) {
                    Status status = data.getStatus();
                    if (status != null) {
                        if ("0".equals(status.getStatus()) && "3".equals(status.getCheckStatus())) {
                            List<SpecPricesItem> specPricesItems = fetchSpecPrices(data.getShopId(), data.getProductId(), doudianCourse);
                            doudianPriveteOrderService.upChannelProduct(doudianCourse, specPricesItems, doudianShopConfig);
                            if (doudianCourse.getChannelId() != null && doudianCourse.getChannelId() > 0L) {
                                AccessToken accessToken = doudianPriveteOrderService.getAccessToken(doudianCourse.getShopId());
                                doudianPriveteOrderService.onlineChannelProductAsync(doudianCourse, doudianShopConfig, accessToken);
                            }
                        }
                    }
                }
            }
        }
    }

    private void processMaterialChange(String appId, String eventSign, String requestBody) {
        MaterialAuditResultForShopRequest request = new MaterialAuditResultForShopRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<MaterialAuditResultForShopParam>> list = request.getRequestBody();
        for (DoudianOpMsgParamRecord<MaterialAuditResultForShopParam> item : list) {
            MaterialAuditResultForShopParam data = item.getData();
            if (data == null || data.getAuditStatus() == null) {
                continue;
            }
            //获取审核状态
            Long auditStatus = data.getAuditStatus();
            MaterialQueryMaterialDetailData data1 = redisService.getCacheObject(doudian_material_upload_prefix + data.getMaterialId());
            if (data1 == null || data1.getMaterialInfo() == null) {
                continue;
            }
            MaterialInfo materialInfo = data1.getMaterialInfo();
            materialInfo.setAuditStatus(auditStatus.intValue());
            if (materialInfo.getAuditStatus() == 3) {
                materialInfo.setByteUrl(data.getByteUrl());
            } else {
                materialInfo.setAuditRejectDesc(data.getAuditStatusDesc());
            }
            data1.setMaterialInfo(materialInfo);
            redisService.setCacheObject(doudian_material_upload_prefix + data.getMaterialId(), data1, 1L, TimeUnit.DAYS);
        }
    }

    private void processRefundSucceed(String appId, String eventSign, String requestBody) {
        RefundRefundSuccessRequest request = new RefundRefundSuccessRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundRefundSuccessParam>> list = request.getRequestBody();
        for (DoudianOpMsgParamRecord<RefundRefundSuccessParam> item : list) {
            RefundRefundSuccessParam data = item.getData();
            //退款成功消息
            //售后Id
            String refundId = "" + data.getAftersaleId();
            System.out.println("退款成功");
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
            if (courseRefund != null) {
                String orderId = courseRefund.getOrderId();
                courseRefund.setRefundStatus(1);
                courseRefund.setRefundType(1);
                CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderId);
                if (courseOrder != null) {
                    Long shopId = data.getShopId();
                    DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(shopId);
                    System.out.println("订单不为空,设置订单为已退款");
                    courseOrder.setOrderStatus(2);
                    courseOrderService.updateCourseOrder(courseOrder);
                    courseRefundService.updateCourseRefund(courseRefund);
                    //退款成功 修改资金状态 备注信息
                    if (doudianShopConfig.getIsPublic() == 1) {
                        wendaoSettlementService.modifyFundsInfo(courseRefund, courseOrder);
                        //公共店铺有出版物记录的修改为退款
                        doudianPriveteOrderService.queryChuBanWuAndSetStatus(courseOrder);
                    } else {
                        doudianPriveteOrderService.modifyFundsInfo(courseRefund, courseOrder);
                    }
                }
            }

        }
    }

    private void processOrderRefundClosed(String appId, String eventSign, String requestBody) {
        RefundRefundClosedRequest request = new RefundRefundClosedRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundRefundClosedParam>> list = request.getRequestBody();
        for (DoudianOpMsgParamRecord<RefundRefundClosedParam> item : list) {
            RefundRefundClosedParam data = item.getData();
            Long afterSaleId = data.getAftersaleId();
            //删除售后单
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId("" + afterSaleId);
            if (courseRefund != null) {
                courseRefundService.deleteCourseRefundById(courseRefund.getId());
            }
            //修改订单状态为已支付
            Long orderIdLong = data.getSId();
            if (orderIdLong == null) {
                orderIdLong = data.getPId();
            }
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId("" + orderIdLong);
            if (courseOrder != null) {
                courseOrder.setOrderStatus(1);
                courseOrderService.updateCourseOrder(courseOrder);
            }
        }
    }

    private void processOrderRefund(String appId, String eventSign, String requestBody) {
        System.out.println("用户发起售后!");
        RefundRefundCreatedRequest request = new RefundRefundCreatedRequest();
        MsgParam param = request.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);
        List<DoudianOpMsgParamRecord<RefundRefundCreatedParam>> list = request.getRequestBody();
        for (DoudianOpMsgParamRecord<RefundRefundCreatedParam> item : list) {
            RefundRefundCreatedParam data = item.getData();
            //申请退款的金额（含运费）
            Long refundAmount = data.getRefundAmount();
            //售后单ID
            Long afterSaleId = data.getAftersaleId();
            Long orderIdLong = data.getSId();
            AccessToken accessToken = doudianPriveteOrderService.getAccessToken(data.getShopId());
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(data.getShopId());
            if (orderIdLong == null) {
                orderIdLong = data.getPId();
            }
            Long reasonCode = data.getReasonCode();
            //如果不是品质问题,则直接退款
            String reason = AfterSaleReasons.getReasonStr(reasonCode);
            String refundId = "" + afterSaleId;

            redisService.setCacheObject(doudian_afterSale_shopId_redis_prefix + refundId, data.getShopId(), 7L, TimeUnit.DAYS);
            BigDecimal returnMoney = BaseWxPayRequest.fen2Yuan(new BigDecimal(refundAmount));
            //创建退款单
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId("" + orderIdLong);
            if (courseOrder == null) {
                continue;
            }
            Long buyerUserId = courseOrder.getBuyerUserId();
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(buyerUserId);
            String openId = wendaoUser.getOpenId();
            CourseRefund courseRefund = courseRefundService.selectCourseRefundByRefundId(refundId);
            CreateWendaoRefundDTO createWendaoRefundDTO = new CreateWendaoRefundDTO();
            createWendaoRefundDTO.setOpenid(openId);
            createWendaoRefundDTO.setRefundReason(reason);
            createWendaoRefundDTO.setOrderId(courseOrder.getOrderId());
            //查询抖店数据
            AfterSaleDetailRequest afterSaleDetailRequest = new AfterSaleDetailRequest();
            afterSaleDetailRequest.setAppKey(doudianShopConfig.getAppKey());
            AfterSaleDetailParam afterSaleDetailParam = afterSaleDetailRequest.getParam();
            afterSaleDetailParam.setAfterSaleId(refundId);
            afterSaleDetailParam.setNeedOperationRecord(true);
            AfterSaleDetailResponse afterSaleDetailResponse = afterSaleDetailRequest.execute(accessToken);
            if (StringUtils.equals("10000", afterSaleDetailResponse.getCode())) {
                AfterSaleDetailData afterSaleDetailData = afterSaleDetailResponse.getData();
                List<String> evidences = afterSaleDetailData.getProcessInfo().getAfterSaleInfo().getEvidence();
                if (CollectionUtils.isNotEmpty(evidences)) {
                    createWendaoRefundDTO.setReceiptImg(StringUtils.join(evidences, ","));
                }
                createWendaoRefund(createWendaoRefundDTO, refundId, courseRefund, returnMoney);
            }
        }
    }

    private boolean processOrderPay(String appId, String eventSign, String requestBody) {
        TradeTradePaidRequest tradeTradePaidRequest = new TradeTradePaidRequest();
        MsgParam param = tradeTradePaidRequest.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);

        List<DoudianOpMsgParamRecord<TradeTradePaidParam>> list = tradeTradePaidRequest.getRequestBody();
        for (DoudianOpMsgParamRecord<TradeTradePaidParam> item : list) {
            TradeTradePaidParam data = item.getData();
            //主订单
            Long pId = data.getPId();
            AccessToken accessToken = doudianPriveteOrderService.getAccessToken(data.getShopId());
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(data.getShopId());
            //按主订单查询订单信息
            OrderOrderDetailRequest orderOrderDetailRequest = new OrderOrderDetailRequest();
            orderOrderDetailRequest.setAppKey(doudianShopConfig.getAppKey());
            OrderOrderDetailParam orderOrderDetailParam = orderOrderDetailRequest.getParam();
            orderOrderDetailParam.setShopOrderId(String.valueOf(pId));
            OrderOrderDetailResponse orderOrderDetailResponse = orderOrderDetailRequest.execute(accessToken);
            OrderOrderDetailData orderOrderDetailData = orderOrderDetailResponse.getData();
            //子订单
            List<Long> sIds = data.getSIds();
            //pay_type
            //Int64
            //订单支付方式： 0: 货到付款 1: 微信 2: 支付宝
            Long payType = data.getPayType();
            //order_type
            //Int64
            //订单类型： 0: 实物 2: 普通虚拟 4: poi核销 5: 三方核销 6: 服务市场
            Long orderType = data.getOrderType();

            DoudianOrderSkuDTO doudianOrderSkuDTO = new DoudianOrderSkuDTO();
            doudianOrderSkuDTO.setOrderType(orderType);
            doudianOrderSkuDTO.setOrderId(String.valueOf(pId));
            doudianOrderSkuDTO.setTotalPayAmount(data.getPayAmount().intValue());

            //7=无需支付（0元单）；8=DOU分期（信用支付）；9=新卡支付；12=先用后付；16=收银台支付,订单支付方式： 0: 货到付款 1: 微信 2: 支付宝
            if (payType == 0) {
                doudianOrderSkuDTO.setPayWay("货到付款");
            }
            if (payType == 1) {
                doudianOrderSkuDTO.setPayWay("微信");
            }
            if (payType == 2) {
                doudianOrderSkuDTO.setPayWay("支付宝");
            }
            if (payType == 7) {
                doudianOrderSkuDTO.setPayWay("无需支付");
            }
            if (payType == 8) {
                doudianOrderSkuDTO.setPayWay("DOU分期");
            }
            if (payType == 9) {
                doudianOrderSkuDTO.setPayWay("新卡支付");
            }
            if (payType == 12) {
                doudianOrderSkuDTO.setPayWay("先用后付");
            }
            if (payType == 16) {
                doudianOrderSkuDTO.setPayWay("收银台支付");
            }
            doudianOrderSkuDTO.setSIds(sIds);
            AjaxResult ajaxResult = this.changeDoudianOrderToPaid(doudianOrderSkuDTO, orderOrderDetailData);
            if (ajaxResult.isError()) {
                return false;
            }
        }
        return true;
    }

    private void processCreateOrder(String appId, String eventSign, String requestBody) {
        TradeTradeCreateRequest tradeTradeCreateRequest = new TradeTradeCreateRequest();
        MsgParam param0 = tradeTradeCreateRequest.getParam();
        param0.setAppId(appId);
        param0.setEventSign(eventSign);
        param0.setRequestBody(requestBody);

        List<DoudianOpMsgParamRecord<TradeTradeCreateParam>> list = tradeTradeCreateRequest.getRequestBody();
        for (DoudianOpMsgParamRecord<TradeTradeCreateParam> item : list) {
            TradeTradeCreateParam data = item.getData();
            //订单创建
            Long pId = data.getPId();
            List<Long> sIds = data.getSIds();
            AccessToken accessToken = doudianPriveteOrderService.getAccessToken(data.getShopId());
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(data.getShopId());
            OrderOrderDetailRequest request = new OrderOrderDetailRequest();
            request.setAppKey(doudianShopConfig.getAppKey());
            OrderOrderDetailParam param = request.getParam();
            param.setShopOrderId(String.valueOf(pId));
            OrderOrderDetailResponse response = request.execute(accessToken);
            OrderOrderDetailData orderData = response.getData();
            //虚拟还是实物
            //boolean isVirtual = false;
            Long orderType = orderData.getShopOrderDetail().getOrderType();
            //0、普通订单 2、虚拟商品订单 4、电子券（poi核销） 5、三方核销 6、服务市场
            //总订单的订单金额
            Long order_amount = orderData.getShopOrderDetail().getOrderAmount();
            //总订单的支付金额
            Long pay_amount = orderData.getShopOrderDetail().getPayAmount();
            List<SkuOrderListItem> skuOrderList = orderData.getShopOrderDetail().getSkuOrderList();
            String doudianOpenId = orderData.getShopOrderDetail().getDoudianOpenId();

            //组装sku参数
            DoudianOrderSkuDTO doudianOrderSkuDTO = new DoudianOrderSkuDTO();
            doudianOrderSkuDTO.setOrderId(String.valueOf(pId));
            doudianOrderSkuDTO.setTotalPayAmount(order_amount.intValue());
            doudianOrderSkuDTO.setDoudianUserId(doudianOpenId);
            //实物订单还是虚拟订单
            doudianOrderSkuDTO.setOrderType(orderType);
            //支付方式,未支付之前不需要
            //额；7=无需支付（0元单）；8=DOU分期（信用支付）；9=新卡支付；12=先用后付；16=收银台支付
            //遍历
            List<SkuOrderInfo> skuInfoList = new ArrayList<>();
            Map<String, SkuOrderListItem> skuOrderListItemMap = new HashMap<>();
            for (SkuOrderListItem skuOrderListItem : skuOrderList) {
                skuOrderListItemMap.put(skuOrderListItem.getOrderId(), skuOrderListItem);
                Long orderAmount = skuOrderListItem.getOrderAmount();
                Long payAmount = skuOrderListItem.getPayAmount();
                SkuOrderInfo skuOrderInfo = new SkuOrderInfo();
                skuOrderInfo.setOrderId(skuOrderListItem.getOrderId());
                skuOrderInfo.setSkuId(String.valueOf(skuOrderListItem.getSkuId()));
                skuOrderInfo.setTotalPaidAmount(orderAmount);
                skuOrderInfo.setAuthorId(skuOrderListItem.getAuthorId());
                //查询课程
                DoudianCourse doudianCourse = doudianCourseService.selectDoudianCourseByProductId(skuOrderListItem.getProductId());
                if (doudianCourse == null) {
                    System.out.println("skuId:" + skuOrderListItem.getSkuId() + "找不到对应的课程!");
                    continue;
                }
                if (doudianCourse.getMultiSku() == 0) {
                    String courseIdDoudian = String.valueOf(doudianCourse.getOutProductId());
                    if (courseIdDoudian.startsWith("90000")) {
                        courseIdDoudian = courseIdDoudian.replace("90000", "");
                    }
                    if (courseIdDoudian.startsWith("80000")) {
                        courseIdDoudian = courseIdDoudian.replace("80000", "");
                    }
                    skuOrderInfo.setCourseId(Long.parseLong(courseIdDoudian));
                } else {
                    //多sku
                    String outSkuId = skuOrderListItem.getOutSkuId();//这个字段前端作为课程id传入
                    if (StringUtils.isNotBlank(outSkuId) && StringUtils.isNumeric(outSkuId)) {
                        skuOrderInfo.setCourseId(Long.parseLong(outSkuId));
                    } else {
                        continue;
                    }
                }
                skuInfoList.add(skuOrderInfo);
            }
            doudianOrderSkuDTO.setSkuInfoList(skuInfoList);
            doudianOrderSkuDTO.setShopId(data.getShopId());
            //单sku商品,如果是多个sku则用户是使用购物车买的
            System.out.println("发送创建订单请求!!参数为:" + JSON.toJSONString(doudianOrderSkuDTO));
            this.createDoudianOrder(doudianOrderSkuDTO, skuOrderListItemMap);
        }
    }

    private void createDoudianOrder(DoudianOrderSkuDTO doudianOrderSkuDTO, Map<String, SkuOrderListItem> skuOrderListItemMap) {
        //调用创建订单
        System.out.println("创建抖店订单,入参:" + JSON.toJSONString(doudianOrderSkuDTO));
        if (StringUtils.isBlank(doudianOrderSkuDTO.getOrderId()) || CollectionUtils.isEmpty(doudianOrderSkuDTO.getSkuInfoList())) {
            System.out.println("参数错误");
            return;
        }
        for (SkuOrderInfo skuOrderInfo : doudianOrderSkuDTO.getSkuInfoList()) {
            SkuOrderListItem skuOrderListItem = skuOrderListItemMap.get(skuOrderInfo.getOrderId());
            CourseOrder courseOrderExist = courseOrderService.selectCourseOrderByOrderId(skuOrderInfo.getOrderId());
            if (courseOrderExist != null) {
                System.out.println("订单已存在");
                return;
            }
            //创建多个订单
            CourseAudit courseAudit = courseAuditService.selectCourseAuditById(skuOrderInfo.getCourseId());
            if (courseAudit == null) {
                System.out.println("课程不存在");
                return;
            }
            //获取推广员信息
            String promoterOpenId = null;
            if (skuOrderInfo.getAuthorId() != null && skuOrderInfo.getAuthorId() > 0L) {
                System.out.println("此单为推广员订单或者老师自己播的");
                //查询是否推广员!
                String promoterPrefix = "doudian_promoter_";
                promoterOpenId = promoterPrefix + skuOrderInfo.getAuthorId();
            }
            Promoter finderPromoter = null;
            if (StringUtils.isNotBlank(promoterOpenId)) {
                //查询推广员逻辑
                finderPromoter = fetchPromoterByOpenId(promoterOpenId, courseAudit);
            }
            Integer appNameType = courseAudit.getAppNameType();
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserByOpenIdPlatformAppNameType(doudianOrderSkuDTO.getDoudianUserId(), appNameType, 11);
            if (wendaoUser == null) {
                //创建用户
                wendaoUser = new WendaoUser();
                wendaoUser.setOpenId(doudianOrderSkuDTO.getDoudianUserId());
                wendaoUser.setPlatform(11);
                wendaoUser.setAppNameType(appNameType);
                wendaoUserService.insertWendaoUser(wendaoUser);
            }
            CourseOrder courseOrder = new CourseOrder();
            courseOrder.setOrderId(skuOrderInfo.getOrderId());
            Integer courseDuration = courseAudit.getCourseDuration();
            courseOrder.setCourseDuration(courseDuration == null ? 0L : courseDuration.longValue());
            courseOrder.setCourseId(courseAudit.getId());
            courseOrder.setTeacherId(courseAudit.getTeacherId());
            courseOrder.setCourseImgUrl(courseAudit.getCoverPicUrl());
            courseOrder.setValidity(courseAudit.getExpirationDay());
            courseOrder.setCourseTitle(skuOrderListItem.getProductName());
            BigDecimal price = BaseWxPayRequest.fen2Yuan(new BigDecimal(skuOrderInfo.getTotalPaidAmount()));
            courseOrder.setCoursePrice(price);
            courseOrder.setAppNameType(wendaoUser.getAppNameType());
            courseOrder.setPayPrice(price);
            courseOrder.setIsCourse(0);
            courseOrder.setOriginalPrice(courseAudit.getOriginalPrice());
            courseOrder.setOrderStatus(0);
            //以这个来判断是否是抖店订单!!!
            courseOrder.setOrderType(11);
            courseOrder.setOrderTime(new Date());
            courseOrder.setFundsType(0);
            courseOrder.setOrderPlatform(11);
            courseOrder.setBuyerUserId(wendaoUser.getId());
            //设置外部订单号为课程Id
            courseOrder.setOutOrderNumber(null);
            if (StringUtils.isNotBlank(skuOrderListItem.getOutProductId())) {
                courseOrder.setOutOrderNumber(skuOrderListItem.getOutProductId());
            }
            courseOrder.setMyEarningsPrice(price);
            if (finderPromoter != null) {
                //推广员逻辑
                PromoterCourse promoterCourse = new PromoterCourse();
                promoterCourse.setCourseId(skuOrderInfo.getCourseId());
                promoterCourse.setPromoterId(finderPromoter.getId());
                List<PromoterCourse> promoterCourses = promoterCourseService.selectPromoterCourseList(promoterCourse);
                if (CollectionUtils.isNotEmpty(promoterCourses)) {
                    courseOrder.setIsPromoter(1);
                    courseOrder.setOrderType(1);
                    PromoterCourse promoterCourse1 = promoterCourses.get(0);
                    Promoter promoter = promoterService.selectPromoterById(finderPromoter.getId());
                    if (promoter != null) {
                        courseOrder.setPromoterId(finderPromoter.getId());
                        courseOrder.setPromoterMobile(promoter.getPromoterPhone());
                        courseOrder.setPromoterName(promoter.getPromoterName());
                        long spreadRate = promoterCourse1.getSpreadRate() == null ? 0L : promoterCourse1.getSpreadRate();
                        courseOrder.setPromotionRatio(Long.toString(spreadRate));
                        if ((int) spreadRate > 0) {
                            BigDecimal multiply = price.multiply(new BigDecimal(spreadRate));
                            BigDecimal divide = multiply.divide(new BigDecimal(100));
                            divide = divide.setScale(2, RoundingMode.DOWN);
                            courseOrder.setPromoterEarningsPrice(divide);
                            courseOrder.setMyEarningsPrice(price.subtract(divide));
                        }
                    }
                }
            }
            courseOrder.setDdShopId(doudianOrderSkuDTO.getShopId());
            courseOrderService.insertCourseOrder(courseOrder);
        }
    }

    private Promoter fetchPromoterByOpenId(String promoterOpenId, CourseAudit courseAudit) {
        if (StringUtils.isNotBlank(promoterOpenId)) {
            WendaoUser wendaoUserPQ = new WendaoUser();
            wendaoUserPQ.setOpenId(promoterOpenId);
            wendaoUserPQ.setPlatform(11);
            wendaoUserPQ.setAppNameType(courseAudit.getAppNameType());
            List<WendaoUser> wendaoUsers = wendaoUserService.selectWendaoUserList(wendaoUserPQ);
            //如果用户存在
            if (CollectionUtils.isNotEmpty(wendaoUsers)) {
                //推广员用户
                WendaoUser wendaoUser = wendaoUsers.get(0);
                //是推广员
                Long id = wendaoUser.getId();
                String idString = String.valueOf(id);
                List<Promoter> promoters = promoterService.selectPromoterListTemp(courseAudit.getTeacherId(), idString);
                if (CollectionUtils.isNotEmpty(promoters)) {
                    for (Promoter p : promoters) {
                        String authorizedAccountList = p.getAuthorizedAccountList();
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(authorizedAccountList)) {
                            List<PromoterAuthorizedAccountDTO> authorizedAccounts = JSON.parseArray(authorizedAccountList, PromoterAuthorizedAccountDTO.class);
                            if (CollectionUtils.isNotEmpty(authorizedAccounts)) {
                                for (PromoterAuthorizedAccountDTO authorizedAccount : authorizedAccounts) {
                                    Long account = authorizedAccount.getAccount();
                                    Integer accountPlatform = authorizedAccount.getPlatform();
                                    if (Objects.equals(account, id) && accountPlatform != null && accountPlatform == 11 && authorizedAccount.getFlag() != null && authorizedAccount.getFlag()) {
                                        return p;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    System.out.println("没有符合的推广员数据!");
                }
            }
        }
        return null;
    }

    private AjaxResult changeDoudianOrderToPaid(DoudianOrderSkuDTO doudianOrderSkuDTO, OrderOrderDetailData orderOrderDetailData) {
        if (StringUtils.isBlank(doudianOrderSkuDTO.getOrderId())) {
            return AjaxResult.error("参数错误");
        }
        boolean isDouyinLite = orderOrderDetailData != null && orderOrderDetailData.getShopOrderDetail() != null
                && orderOrderDetailData.getShopOrderDetail().getBType() != null
                && orderOrderDetailData.getShopOrderDetail().getBType().intValue() == 11;//2是抖音
        List<SkuOrderListItem> skuOrderList = orderOrderDetailData.getShopOrderDetail().getSkuOrderList();
        Map<String, SkuOrderListItem> orderList = new HashMap<>();
        for (SkuOrderListItem skuOrderItem : skuOrderList) {
            orderList.put(skuOrderItem.getOrderId(), skuOrderItem);
        }
        for (Long orderId : doudianOrderSkuDTO.getSIds()) {
            String orderIdStr = String.valueOf(orderId);
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(orderIdStr);
            if (courseOrder == null) {
                return AjaxResult.error("订单还没有创建!");
            }
            if (courseOrder.getOrderStatus() == 1) {
                //已支付
                continue;
            }
            //获取抖店订单信息
            SkuOrderListItem skuOrderListItem = orderList.get(orderIdStr);
            if (skuOrderListItem == null) {
                continue;
            }
            BigDecimal price = BaseWxPayRequest.fen2Yuan(new BigDecimal(skuOrderListItem.getPayAmount()));
            courseOrder.setPayPrice(price);
            courseOrder.setMyEarningsPrice(price);
            courseOrder.setSettleStatus(1);
            courseOrder.setOrderStatus(1);
            courseOrder.setPayTime(new Date());
            long spreadRate = 0L;
            if (StringUtils.isNotBlank(courseOrder.getPromotionRatio()) && StringUtils.isNumeric(courseOrder.getPromotionRatio())) {
                spreadRate = Long.parseLong(courseOrder.getPromotionRatio());
            }
            if ((int) spreadRate > 0) {
                BigDecimal multiply = price.multiply(new BigDecimal(spreadRate));
                BigDecimal divide = multiply.divide(new BigDecimal(100));
                divide = divide.setScale(2, RoundingMode.DOWN);
                courseOrder.setPromoterEarningsPrice(divide);
                courseOrder.setMyEarningsPrice(price.subtract(divide));
            }
            //支付方式
            if (StringUtils.isNotBlank(doudianOrderSkuDTO.getPayWay())) {
                courseOrder.setPayWay(doudianOrderSkuDTO.getPayWay());
            } else {
                courseOrder.setPayWay("抖音支付");
            }
            boolean isWxMiniappOpen = shopGotowxWhitelistService.isWxMiniappOpenByTeacherId(courseOrder.getTeacherId());
            boolean isDouyinMiniappOpen = shopGotowxWhitelistService.isDouyinMiniappOpenByTeacherId(courseOrder.getTeacherId());
            courseOrder.setTradingOrderNumber(orderOrderDetailData.getShopOrderDetail().getChannelPaymentNo());
            courseOrderService.updateCourseOrder(courseOrder);
            //查询抖店配置
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(orderOrderDetailData.getShopOrderDetail().getShopId());
            //创建流水
            if (doudianShopConfig.getIsPublic() == 1) {
                wendaoSettlementService.createFundsRecord(courseOrder, skuOrderListItem);
            } else {
                //如果不是公共店铺则不创建流水，不对账户加金额
                doudianPriveteOrderService.createFundsRecord(courseOrder, doudianShopConfig);
            }
            String clientTokenKey = dyConfig.getClientAccessTokenKey() + ":" + courseOrder.getAppNameType();
            String clentAccessToken = redisService.getCacheObject(clientTokenKey);
            String key = "wxcodedoudian:" + orderIdStr;
            String link = redisService.getCacheObject(key);
            //微信放在前面,抖音放在后面
            List<String> sendLinkList = new ArrayList<>();
            if (StringUtils.isBlank(link)) {
                if (isWxMiniappOpen) {
                    GenerateUrlLinkRequestExt dto = new GenerateUrlLinkRequestExt();
                    dto.setAppId("wx0e5e01d239197bb1");
                    dto.setPath("pages_mine/bind_phone/bind_phone");
                    dto.setQuery("orderId=" + orderIdStr + "&platform=1&appNameType=2");
                    dto.setEnvVersion("release");
                    dto.setExpireInterval(30);
                    dto.setExpireType(1);
                    dto.setIsExpire(true);
                    AjaxResult ajaxResult = wxMaLinkService.generateUrlLink(dto);
                    if (ajaxResult.isSuccess()) {
                        link = (String) ajaxResult.get("data");
                        sendLinkList.add(link);
                        redisService.setCacheObject(key, link, 30L, TimeUnit.DAYS);
                    }
                }
                if (isDouyinMiniappOpen && StringUtils.isNotBlank(clentAccessToken)) {
                    GenerateUrlLinkDTO generateUrlLinkDTO = new GenerateUrlLinkDTO();
                    generateUrlLinkDTO.setApp_id(Appids.getAppidByAppNameType(courseOrder.getAppNameType()));
                    if (isDouyinLite) {
                        generateUrlLinkDTO.setApp_name("douyinlite");
                    } else {
                        generateUrlLinkDTO.setApp_name("douyin");
                    }
                    //加160天
                    long time = (new Date().getTime() + 160L * 24L * 60L * 60L * 1000L) / 1000;
                    generateUrlLinkDTO.setExpire_time(time);
                    // 创建一个 Map 来存储所有的参数
                    Map<String, String> queryParams = new HashMap<>();
                    queryParams.put("orderId", orderIdStr);
                    queryParams.put("platform", "0");
                    queryParams.put("appNameType", String.valueOf(courseOrder.getAppNameType()));
                    queryParams.put("buyerUserId", String.valueOf(courseOrder.getBuyerUserId()));
                    String jsonQuery = JSON.toJSONString(queryParams);
                    generateUrlLinkDTO.setQuery(jsonQuery);
                    generateUrlLinkDTO.setPath("pages_mine/bind_phone/bind_phone");
                    GenerateUrlLinkResult generateUrlLinkResult = WxKuaishouPayController.postRequest(generateUrlLinkDTO, clentAccessToken);
                    if (generateUrlLinkResult != null && generateUrlLinkResult.getErr_no() == 0) {
                        link = generateUrlLinkResult.getData().getUrl_link();
                        sendLinkList.add(link);
                        redisService.setCacheObject(key, link, 160L, TimeUnit.DAYS);
                    }
                }
            }
            AccessToken accessToken = doudianPriveteOrderService.getAccessToken(orderOrderDetailData.getShopOrderDetail().getShopId());
            Long dShopId = orderOrderDetailData.getShopOrderDetail().getShopId();
            //从链接中获取最后一个/后面的字符串，私有店铺
            // https://z.douyin.com/xEcuol6
            boolean isLjcxcj = Objects.equals(dShopId, OrderAppConfig.ljcxcjIdLong);
            String urlCode = link.substring(link.lastIndexOf("/") + 1);
            String smsUrl = link.replace("https://", "");
            //私有店铺
            redisService.setCacheObject("doudian_Sms_Url_Code:" + urlCode, link, 160L, TimeUnit.DAYS);
            boolean isChengMeiJiaoYu = Objects.equals(dShopId, OrderAppConfig.ChengMeiJiaoYu);
            boolean isWendaoJiaoyu = Objects.equals(dShopId, OrderAppConfig.WendaoJiaoyu);
            boolean isWendao = Objects.equals(dShopId, OrderAppConfig.wendaoShopIdLong);
            boolean isWending = Objects.equals(dShopId, OrderAppConfig.wendingShopIdLong);
            boolean isWnedaoNew = Objects.equals(dShopId, OrderAppConfig.wendaoNewShopIdLong);
            boolean isYgjjdp = Objects.equals(dShopId, OrderAppConfig.ygjydpIdLong);
            boolean isWBBShop = Objects.equals(dShopId, OrderAppConfig.WBB_SHOP_ID);
            boolean isWenDa = Objects.equals(dShopId, OrderAppConfig.WenDa);
            //发送短信链接
            SmsSendRequest request = new SmsSendRequest();
            request.setAppKey(doudianShopConfig.getAppKey());
            SmsSendParam param = request.getParam();
            param.setSmsAccount("7f07982f");
            if (isChengMeiJiaoYu) {
                param.setSign("成美数字");
                param.setTemplateId("ST_85e003e7");
            } else if (isWendaoJiaoyu) {
                param.setSign("问到");
                param.setTemplateId("ST_85e09750");
            } else if (isWendao) {
                param.setSign("问到课堂");
                param.setTemplateId("ST_7f954828");
            } else if (isWending) {
                param.setSign("问到课堂");
                param.setTemplateId("ST_7f95006a");
            } else if (isWnedaoNew) {
                param.setSign("问到课堂");
                param.setTemplateId("ST_7fe400ec");
            } else if (isLjcxcj) {
                param.setSmsAccount("801e36da");
                param.setSign("零基础学裁剪");
                param.setTemplateId("ST_801e8a4f");
            } else if (isYgjjdp) {
                param.setSmsAccount("804090b6");
                param.setSign("杨工教育店铺");
                param.setTemplateId("ST_8040b358");
            } else if (isWBBShop) {
                param.setSmsAccount("80729ee1");
                param.setSign("伍伯伯吉他铺");
                param.setTemplateId("ST_8072ccdd");
            } else if (isWenDa) {
                param.setSmsAccount("7f07982f");
                param.setSign("问到课堂");
                param.setTemplateId("ST_8284fd73");
            }
            String smsSendProductName = (StringUtils.isBlank(courseOrder.getCourseTitle()) ? "课程" : courseOrder.getCourseTitle());
            //如果smsSendProductName超过6个中文字则截取6个中文字,后面加三个点...,不超过则不截取
            // 判断字符串长度是否超过6个字符
            if (smsSendProductName.length() > 6) {
                // 截取前6个字符并添加省略号
                smsSendProductName = smsSendProductName.substring(0, 6) + "...";
                List<String> bandList = new ArrayList<>();
                bandList.add("短视频剪辑教...");
                bandList.add("财商新思维【...");
                bandList.add("唱歌声音测评...");
                if (bandList.contains(smsSendProductName)) {
                    smsSendProductName = "课程";
                }
            }
            //param.setTemplateParam("{\"productName\":\"" + smsSendProductName + "\",\"learnUrl\":\"" + smsUrl + "\"}");
            param.setPostTel(skuOrderListItem.getEncryptPostTel());
            for(String sendLink:sendLinkList){
                if(StringUtils.isNotBlank(sendLink)){
                    param.setTemplateParam("{\"productName\":\"" + smsSendProductName + "\",\"learnUrl\":\"" + sendLink.replace("https://", "") + "\"}");
                    SmsSendResponse response = request.execute(accessToken);
                    System.out.println(com.alibaba.fastjson.JSON.toJSONString(response));
                    /**
                     * 发送短信记录短信id，稍后检查成功失败状态
                     */
                    if ("10000".equals(response.getCode()) && response.getData() != null) {
                        courseOrder.setDdSmsStatus(1);
                        courseOrder.setDdSmsId(response.getData().getMessageId());
                        redisService.setCacheObject(doudian_sms_send_param_prefix + response.getData().getMessageId(), param, 35L, TimeUnit.DAYS);
                        redisService.setCacheObject(doudian_sms_send_sms_id_order_id_prefix + response.getData().getMessageId(), courseOrder.getOrderId(), 3L, TimeUnit.DAYS);
                        courseOrderService.updateCourseOrder(courseOrder);
                    } else {
                        courseOrder.setDdSmsStatus(2);
                        log.error("发送短信失败，订单号：" + orderIdStr + "，原因：" + response.getMsg() + "，" + response.getSubMsg());
                        courseOrderService.updateCourseOrder(courseOrder);
                    }
                }
            }
            redisService.setCacheObject(doudian_resend_sms_redis_prefix + orderIdStr, urlCode, 29L, TimeUnit.DAYS);
            //设置队列
            try {
                if (isWendao || isWending || isWnedaoNew || isWenDa) {
                    if (StringUtils.isNotBlank(link) && link.contains("douyin")) {
                        redisService.addOrderIdToQueue(dShopId, orderIdStr);
                    }
                }
            } catch (Exception e) {
                log.error("redisService.addOrderIdToQueue(dShopId, orderIdStr)异常", e);
            }

            //判断是否虚拟商品
            Long orderType = skuOrderListItem.getOrderType();
            if (orderType != null && orderType == 5L) {
                CouponsSyncV2Request couponsSyncV2Request = new CouponsSyncV2Request();
                couponsSyncV2Request.setAppKey(doudianShopConfig.getAppKey());
                CouponsSyncV2Param couponsSyncV2Param = couponsSyncV2Request.getParam();
                //子订单Id
                couponsSyncV2Param.setOrderId(orderIdStr);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                CertListItem certListItem = new CertListItem();
                //领取地址
                String getCouponsUrl = "https://" + smsUrl;
                certListItem.setCertLink(getCouponsUrl);
                certListItem.setCertNo(urlCode);
                certListItem.setGrantTime(sdf.format(new Date()));
                couponsSyncV2Param.setCertList(Collections.singletonList(certListItem));
                CouponsSyncV2Response couponsSyncV2Response = couponsSyncV2Request.execute(accessToken);
                System.out.println("同步卡券结果:" + JSON.toJSONString(couponsSyncV2Response));
                if ("10000".equalsIgnoreCase(couponsSyncV2Response.getCode())) {
                    //存入redis
                    redisService.setCacheObject(doudian_order_coupons_redis_prefix + orderIdStr, urlCode, 100L, TimeUnit.DAYS);
                    //存入店铺id
                    redisService.setCacheObject(doudian_order_coupons_redis_prefix_shop_id + orderIdStr, dShopId, 100L, TimeUnit.DAYS);
                }
                DdHexiaoWhitelist ddHexiaoWhitelist = ddHexiaoWhitelistService.selectDdHexiaoWhitelistByTeacherId(courseOrder.getTeacherId());
                if (ddHexiaoWhitelist != null && ddHexiaoWhitelist.getOpenStatus() == 1 && courseOrder.getDdShopId() != null && courseOrder.getDdShopId() > 0L) {
                    if (doudianShopConfig.getIsSupportThirdCoupon() == 1 && doudianShopConfig.getOpenThirdCoupon() == 1) {
                        doudianPriveteOrderService.whiteListHeXiao(courseOrder.getOrderId(), courseOrder.getDdShopId(), doudianShopConfig.getAppKey());
                    }
                }
            }
            if (skuOrderListItem.getFirstCid() != null && skuOrderListItem.getFirstCid() == 20015L) {
                int rows = doudianPriveteOrderService.createChuBanWuOrder(courseOrder, skuOrderListItem);
                if (rows > 0) {
                    System.out.println("创建出版物订单扣点记录成功");
                } else {
                    System.out.println("创建出版物订单扣点记录失败");
                }
            }
        }
        return AjaxResult.success();
    }

    private void processProductChange(String appId, String eventSign, String requestBody) {
        ProductChangeRequest productChangeRequest = new ProductChangeRequest();
        MsgParam param = productChangeRequest.getParam();
        param.setAppId(appId);
        param.setEventSign(eventSign);
        param.setRequestBody(requestBody);

        List<DoudianOpMsgParamRecord<ProductChangeParam>> list = productChangeRequest.getRequestBody();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (DoudianOpMsgParamRecord<ProductChangeParam> item : list) {
            ProductChangeParam data = item.getData();
            Long productId = data.getProductId();
            //先查出商品
            DoudianCourse doudianCourse = doudianCourseService.selectDoudianCourseByProductId(productId);
            if (doudianCourse == null) {
                continue;
            }
            DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(data.getShopId());
            //商品审核通过
            if (data.getEvent() == 4) {
                doudianCourse.setCheckStatus(3L);
                doudianCourse.setStatus(0L);
                doudianCourseService.updateDoudianCourse(doudianCourse);

                List<SpecPricesItem> specPricesItems = fetchSpecPrices(data.getShopId(), data.getProductId(), doudianCourse);


                //同时上架渠道商品
                doudianPriveteOrderService.upChannelProduct(doudianCourse, specPricesItems, doudianShopConfig);
                if (doudianCourse.getChannelId() != null && doudianCourse.getChannelId() > 0L) {
                    AccessToken accessToken = doudianPriveteOrderService.getAccessToken(doudianCourse.getShopId());
                    doudianPriveteOrderService.onlineChannelProductAsync(doudianCourse, doudianShopConfig, accessToken);
                }


                //商品变更事件： 1: 商品创建； 2: 商品保存； 3:商品提交审核； 4：商品审核通过； 5：商品审核不通过； 6：商品被删除； 7：商品从回收站恢复； 8：商品封禁； 9：解除商品封禁； 10：下架商品； 11：上架商品； 12：商品售空； 13：终止审核商品； 14：审核通过待上架；17：彻底删除商品
                //添加商品
            }
            //商品审核不通过
            if (data.getEvent() == 5) {
                doudianCourse.setCheckStatus(4L);
                if (StringUtils.isNotBlank(data.getReason())) {
                    doudianCourse.setCheckFailReason(data.getReason());
                }
                doudianCourseService.updateDoudianCourse(doudianCourse);
            }
            //商品封禁
            if (data.getEvent() == 5) {
                doudianCourse.setStatus(1L);
                doudianCourse.setCheckStatus(5L);
                if (StringUtils.isNotBlank(data.getReason())) {
                    doudianCourse.setCheckFailReason(data.getReason());
                }
                doudianCourseService.updateDoudianCourse(doudianCourse);
            }
            //下架商品
            if (data.getEvent() == 10) {
                doudianCourse.setStatus(1L);
                doudianCourseService.updateDoudianCourse(doudianCourse);
            }
            //上架商品
            if (data.getEvent() == 11) {
                doudianCourse.setStatus(0L);
                doudianCourse.setCheckStatus(3L);
                doudianCourseService.updateDoudianCourse(doudianCourse);
                List<SpecPricesItem> specPricesItems = fetchSpecPrices(data.getShopId(), data.getProductId(), doudianCourse);
                //同时上架渠道商品
                doudianPriveteOrderService.upChannelProduct(doudianCourse, specPricesItems, doudianShopConfig);
                if (doudianCourse.getChannelId() != null && doudianCourse.getChannelId() > 0L) {
                    AccessToken accessToken = doudianPriveteOrderService.getAccessToken(doudianCourse.getShopId());
                    doudianPriveteOrderService.onlineChannelProductAsync(doudianCourse, doudianShopConfig, accessToken);
                }
            }
            //审核通过待上架
            if (data.getEvent() == 14) {
                doudianCourse.setCheckStatus(3L);
                doudianCourseService.updateDoudianCourse(doudianCourse);
            }
        }
    }

    private List<SpecPricesItem> fetchSpecPrices(Long shopId, Long productId, DoudianCourse doudianCourse) {
        DoudianShopConfig doudianShopConfig = doudianShopConfigService.selectDoudianShopConfigByShopId(shopId);
        if (doudianShopConfig == null) {
            return Collections.emptyList();
        }
        AccessToken accessToken = doudianPriveteOrderService.getAccessToken(shopId);
        ProductDetailRequest request = new ProductDetailRequest();
        request.setAppKey(doudianShopConfig.getAppKey());
        ProductDetailParam param11 = request.getParam();
        param11.setProductId(String.valueOf(productId));
        ProductDetailResponse response = request.execute(accessToken);
        if ("10000".equals(response.getCode())) {
            ProductDetailData productDetailData = response.getData();
            List<SpecPricesItem> specPrices = productDetailData.getSpecPrices();
            DoudianCourse updateDoudianCourse = new DoudianCourse();
            updateDoudianCourse.setId(doudianCourse.getId());
            updateDoudianCourse.setSpecPrices(JSON.toJSONString(specPrices));
            updateDoudianCourse.setSpecPricesV2(JSON.toJSONString(specPrices));
            doudianCourseService.updateDoudianCourse(updateDoudianCourse);
            return productDetailData.getSpecPrices();
        }
        return Collections.emptyList();
    }

    private void createWendaoRefund(CreateWendaoRefundDTO createWendaoRefundDTO, String refundId, CourseRefund courseRefundOld, BigDecimal returnMoney) {
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId(createWendaoRefundDTO.getOrderId());
        if (courseOrder == null) {
            return;
        }
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserByOpenid(createWendaoRefundDTO.getOpenid(), courseOrder.getAppNameType());
        Long wendaoUserId = wendaoUser.getId();
        if (!Objects.equals(wendaoUserId, courseOrder.getBuyerUserId())) {
            return;
        }
        //创建本侧退款单
        CourseRefund courseRefund = courseRefundOld == null ? new CourseRefund() : courseRefundOld;
        courseRefund.setBuyerUserId(wendaoUserId);
        courseRefund.setOrderId(courseOrder.getOrderId());
        courseRefund.setRefundPrice(returnMoney);
        //reason
        courseRefund.setRefundReason(createWendaoRefundDTO.getRefundReason());
        courseRefund.setSupplimentalDescription(createWendaoRefundDTO.getSupplimentalDescription());
        courseRefund.setReceiptImg(createWendaoRefundDTO.getReceiptImg());
        courseRefund.setIsDelete(0);
        //需要退款审核
        courseRefund.setRefundStatus(0);
        courseRefund.setRefundType(0);
        courseRefund.setRefundPlatform(11);
        //设置来源app,问到课堂或问到好课
        courseRefund.setAppNameType(courseOrder.getAppNameType());
        //设置来源
        courseRefund.setRefundId(refundId);
        courseRefund.setOutRefundId(refundId);
        Long courseId = courseOrder.getCourseId();
        String courseTitle = courseOrder.getCourseTitle();
        courseRefund.setCourseId(courseId);
        courseRefund.setCourseTitle(courseTitle);
        courseRefund.setCoursePrice(courseOrder.getCoursePrice());
        courseRefund.setOriginalPrice(courseOrder.getOriginalPrice());
        courseRefund.setCourseImgUrl(courseOrder.getCourseImgUrl());
        courseRefund.setBuyerUserName(wendaoUser.getUserName());
        courseRefund.setBuyerUserMobile(wendaoUser.getTelNumber());
        courseRefund.setRefundTime(new Date());
        courseRefund.setTeacherId(courseOrder.getTeacherId());
        courseRefund.setOrderTime(courseOrder.getPayTime());
        courseRefund.setUpdateTime(new Date());

        courseRefund.setCourseDuration(courseOrder.getCourseDuration());
        courseRefund.setStudyDuration(courseOrder.getStudyDuration());

        if (courseRefund.getId() != null) {
            //状态不修改,有可能是消息后到
            courseRefund.setRefundStatus(null);
            courseRefund.setRefundType(null);
            courseRefundService.updateCourseRefund(courseRefund);
        } else {
            courseRefundService.insertCourseRefund(courseRefund);
        }
        //courseOrder.setOrderStatus(4);
        courseOrderService.updateCourseOrderNotTwo(createWendaoRefundDTO.getOrderId(), 4);

        //退款通知
        MessageNotification messageNotification = new MessageNotification();
        messageNotification.setId(courseOrder.getBuyerUserId());
        messageNotification.setTeacherId(courseOrder.getTeacherId());
        messageNotification.setUserId(null);
        messageNotification.setNoticeTitle("「 退款通知 」 用户购买的课程《" + courseOrder.getCourseTitle() + "》发起了退款");
        String userName = "用户" + courseOrder.getBuyerUserId();
        userName = com.wendao101.common.core.utils.StringUtils.isBlank(courseOrder.getBuyerUserName()) ? userName : courseOrder.getBuyerUserName();
        messageNotification.setNoticeContent("用户昵称：" + userName + "\\n" + "退款金额：" + courseOrder.getPayPrice() + "\\n" + "课程名称：" + courseOrder.getCourseTitle());
        messageNotification.setNoticeType(7);
        messageNotification.setIsDelete(0);
        messageNotification.setReadStatus(0);
        messageNotification.setCreateTime(DateUtils.getNowDate());
        messageNotification.setUpdateTime(DateUtils.getNowDate());
        messageNotificationService.insertMessageNotification(messageNotification);

        //执行自动退款
        if (courseRefund.getRefundPlatform() == 11) {
            if (courseOrder.getDdHexiaoStatus() != null && courseOrder.getDdHexiaoStatus() == 1) {
                return;
            }
            //如果是虚拟商品
            String refundReason = courseRefund.getRefundReason();
            if (StringUtils.isNotBlank(refundReason)) {
                if (refundReason.contains("不符")) {
                    redisService.setCacheObject(REFUND_REASON_KEY_PREFIX + courseRefund.getOrderId(), refundReason, 30L, TimeUnit.DAYS);
                    return;
                }
                if (refundReason.contains("无法")) {
                    redisService.setCacheObject(REFUND_REASON_KEY_PREFIX + courseRefund.getOrderId(), refundReason, 30L, TimeUnit.DAYS);
                    return;
                }
                if (AfterSaleReasonsNeedHandle.fetchReasonList().contains(refundReason)) {
                    redisService.setCacheObject(REFUND_REASON_KEY_PREFIX + courseRefund.getOrderId(), refundReason, 30L, TimeUnit.DAYS);
                    return;
                }
                String refundReasonRedis = redisService.getCacheObject(REFUND_REASON_KEY_PREFIX + courseRefund.getOrderId());
                if (StringUtils.isBlank(refundReasonRedis)) {
                    //处理退款
                    courseRefundService.autoRefundProcess(courseRefund);
                }
            }
        }
        //发送微信申请退款消息
        RefundApplyMessage refundMessage = new RefundApplyMessage();
        refundMessage.setTeacherId(courseOrder.getTeacherId());
        refundMessage.setOrderId(courseOrder.getOrderId());
        refundMessage.setCourseTitle(courseOrder.getCourseTitle());
        refundMessage.setRefundReason(courseRefund.getRefundReason());
        refundMessage.setRefundPrice(courseRefund.getRefundPrice());
        refundMessage.setRefundTime(courseRefund.getRefundTime());
        wxSendMessageService.sendRefundApplyMessage(refundMessage);
    }
}
