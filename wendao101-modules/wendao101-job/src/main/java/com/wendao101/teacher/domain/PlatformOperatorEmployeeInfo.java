package com.wendao101.teacher.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 平台经营者和从业人员身份信息报送表对象 platform_operator_employee_info
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public class PlatformOperatorEmployeeInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 平台名称 */
    @Excel(name = "平台名称")
    private String platformName;

    /** 是否已取得登记证照(Y/N) */
    @Excel(name = "是否已取得登记证照", readConverterExp = "Y=是,N=否")
    private String hasCertificate;

    /** 名称(姓名)-已登记时填写 */
    @Excel(name = "名称(姓名)")
    private String registeredName;

    /** 统一社会信用代码-已登记时填写 */
    @Excel(name = "统一社会信用代码")
    private String registeredCreditCode;

    /** 专业服务机构标识(00|否,04|其他,05|直播) */
    @Excel(name = "专业服务机构标识", readConverterExp = "00=否,04=其他,05=直播")
    private String professionalServiceAgency;

    /** 姓名-未登记时填写 */
    @Excel(name = "姓名")
    private String personalName;

    /** 证件类型 */
    @Excel(name = "证件类型")
    private String idType;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String idNumber;

    /** 国家或地区代码 */
    @Excel(name = "国家或地区代码")
    private String countryRegion;

    /** 是否存在免于报送收入信息情形 */
    @Excel(name = "是否存在免于报送收入信息情形", readConverterExp = "Y=是,N=否")
    private String isIncomeExempt;

    /** 免报类型 */
    @Excel(name = "免报类型")
    private String exemptionType;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 店铺(用户)名称 */
    @Excel(name = "店铺名称")
    private String shopName;

    /** 店铺唯一标识码 */
    @Excel(name = "店铺唯一标识码")
    private String shopUniqueId;

    /** 网址链接 */
    @Excel(name = "网址链接")
    private String shopUrl;

    /** 结算(支付)账户信息 */
    @Excel(name = "结算账户信息")
    private String settlementAccount;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 经营开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "经营开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date businessStartDate;

    /** 经营结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "经营结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date businessEndDate;

    /** 信息状态标识(01|新增,02|变更,03|退出) */
    @Excel(name = "信息状态标识", readConverterExp = "01=新增,02=变更,03=退出")
    private String infoStatus;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 入驻的平台,1:微信,2:快手 */
    @Excel(name = "入驻的平台", readConverterExp = "1=微信,2=快手")
    private Integer orderPlatform;

    /** 来源的APP,1:问到好课,2:问到课堂 */
    @Excel(name = "来源的APP", readConverterExp = "1=问到好课,2=问到课堂")
    private Integer appNameType;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPlatformName(String platformName) 
    {
        this.platformName = platformName;
    }

    public String getPlatformName() 
    {
        return platformName;
    }
    public void setHasCertificate(String hasCertificate) 
    {
        this.hasCertificate = hasCertificate;
    }

    public String getHasCertificate() 
    {
        return hasCertificate;
    }
    public void setRegisteredName(String registeredName) 
    {
        this.registeredName = registeredName;
    }

    public String getRegisteredName() 
    {
        return registeredName;
    }
    public void setRegisteredCreditCode(String registeredCreditCode) 
    {
        this.registeredCreditCode = registeredCreditCode;
    }

    public String getRegisteredCreditCode() 
    {
        return registeredCreditCode;
    }
    public void setProfessionalServiceAgency(String professionalServiceAgency) 
    {
        this.professionalServiceAgency = professionalServiceAgency;
    }

    public String getProfessionalServiceAgency() 
    {
        return professionalServiceAgency;
    }
    public void setPersonalName(String personalName) 
    {
        this.personalName = personalName;
    }

    public String getPersonalName() 
    {
        return personalName;
    }
    public void setIdType(String idType) 
    {
        this.idType = idType;
    }

    public String getIdType() 
    {
        return idType;
    }
    public void setIdNumber(String idNumber) 
    {
        this.idNumber = idNumber;
    }

    public String getIdNumber() 
    {
        return idNumber;
    }
    public void setCountryRegion(String countryRegion) 
    {
        this.countryRegion = countryRegion;
    }

    public String getCountryRegion() 
    {
        return countryRegion;
    }
    public void setIsIncomeExempt(String isIncomeExempt) 
    {
        this.isIncomeExempt = isIncomeExempt;
    }

    public String getIsIncomeExempt() 
    {
        return isIncomeExempt;
    }
    public void setExemptionType(String exemptionType) 
    {
        this.exemptionType = exemptionType;
    }

    public String getExemptionType() 
    {
        return exemptionType;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setShopName(String shopName) 
    {
        this.shopName = shopName;
    }

    public String getShopName() 
    {
        return shopName;
    }
    public void setShopUniqueId(String shopUniqueId) 
    {
        this.shopUniqueId = shopUniqueId;
    }

    public String getShopUniqueId() 
    {
        return shopUniqueId;
    }
    public void setShopUrl(String shopUrl) 
    {
        this.shopUrl = shopUrl;
    }

    public String getShopUrl() 
    {
        return shopUrl;
    }
    public void setSettlementAccount(String settlementAccount) 
    {
        this.settlementAccount = settlementAccount;
    }

    public String getSettlementAccount() 
    {
        return settlementAccount;
    }
    public void setContactName(String contactName) 
    {
        this.contactName = contactName;
    }

    public String getContactName() 
    {
        return contactName;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setBusinessStartDate(Date businessStartDate) 
    {
        this.businessStartDate = businessStartDate;
    }

    public Date getBusinessStartDate() 
    {
        return businessStartDate;
    }
    public void setBusinessEndDate(Date businessEndDate) 
    {
        this.businessEndDate = businessEndDate;
    }

    public Date getBusinessEndDate() 
    {
        return businessEndDate;
    }
    public void setInfoStatus(String infoStatus) 
    {
        this.infoStatus = infoStatus;
    }

    public String getInfoStatus() 
    {
        return infoStatus;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setOrderPlatform(Integer orderPlatform) 
    {
        this.orderPlatform = orderPlatform;
    }

    public Integer getOrderPlatform() 
    {
        return orderPlatform;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("platformName", getPlatformName())
            .append("hasCertificate", getHasCertificate())
            .append("registeredName", getRegisteredName())
            .append("registeredCreditCode", getRegisteredCreditCode())
            .append("professionalServiceAgency", getProfessionalServiceAgency())
            .append("personalName", getPersonalName())
            .append("idType", getIdType())
            .append("idNumber", getIdNumber())
            .append("countryRegion", getCountryRegion())
            .append("isIncomeExempt", getIsIncomeExempt())
            .append("exemptionType", getExemptionType())
            .append("address", getAddress())
            .append("shopName", getShopName())
            .append("shopUniqueId", getShopUniqueId())
            .append("shopUrl", getShopUrl())
            .append("settlementAccount", getSettlementAccount())
            .append("contactName", getContactName())
            .append("contactPhone", getContactPhone())
            .append("businessStartDate", getBusinessStartDate())
            .append("businessEndDate", getBusinessEndDate())
            .append("infoStatus", getInfoStatus())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .append("teacherId", getTeacherId())
            .append("orderPlatform", getOrderPlatform())
            .append("appNameType", getAppNameType())
            .toString();
    }
}
