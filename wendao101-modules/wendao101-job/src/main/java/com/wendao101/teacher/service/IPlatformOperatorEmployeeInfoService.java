package com.wendao101.teacher.service;

import com.wendao101.teacher.domain.PlatformOperatorEmployeeInfo;

import java.util.List;

/**
 * 平台经营者和从业人员身份信息报送表Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IPlatformOperatorEmployeeInfoService 
{
    /**
     * 查询平台经营者和从业人员身份信息报送表
     * 
     * @param id 平台经营者和从业人员身份信息报送表主键
     * @return 平台经营者和从业人员身份信息报送表
     */
    public PlatformOperatorEmployeeInfo selectPlatformOperatorEmployeeInfoById(Long id);

    /**
     * 查询平台经营者和从业人员身份信息报送表列表
     * 
     * @param platformOperatorEmployeeInfo 平台经营者和从业人员身份信息报送表
     * @return 平台经营者和从业人员身份信息报送表集合
     */
    public List<PlatformOperatorEmployeeInfo> selectPlatformOperatorEmployeeInfoList(PlatformOperatorEmployeeInfo platformOperatorEmployeeInfo);

    /**
     * 新增平台经营者和从业人员身份信息报送表
     * 
     * @param platformOperatorEmployeeInfo 平台经营者和从业人员身份信息报送表
     * @return 结果
     */
    public int insertPlatformOperatorEmployeeInfo(PlatformOperatorEmployeeInfo platformOperatorEmployeeInfo);

    /**
     * 修改平台经营者和从业人员身份信息报送表
     * 
     * @param platformOperatorEmployeeInfo 平台经营者和从业人员身份信息报送表
     * @return 结果
     */
    public int updatePlatformOperatorEmployeeInfo(PlatformOperatorEmployeeInfo platformOperatorEmployeeInfo);

    /**
     * 批量删除平台经营者和从业人员身份信息报送表
     * 
     * @param ids 需要删除的平台经营者和从业人员身份信息报送表主键集合
     * @return 结果
     */
    public int deletePlatformOperatorEmployeeInfoByIds(Long[] ids);

    /**
     * 删除平台经营者和从业人员身份信息报送表信息
     * 
     * @param id 平台经营者和从业人员身份信息报送表主键
     * @return 结果
     */
    public int deletePlatformOperatorEmployeeInfoById(Long id);

    /**
     * 根据老师ID查询平台经营者信息
     * 
     * @param teacherId 老师ID
     * @return 平台经营者信息列表
     */
    public List<PlatformOperatorEmployeeInfo> selectPlatformOperatorEmployeeInfoByTeacherId(Long teacherId);

    /**
     * 根据店铺唯一标识码查询平台经营者信息
     * 
     * @param shopUniqueId 店铺唯一标识码
     * @return 平台经营者信息
     */
    public PlatformOperatorEmployeeInfo selectPlatformOperatorEmployeeInfoByShopId(String shopUniqueId);

    /**
     * 根据证件号码查询平台经营者信息
     * 
     * @param idNumber 证件号码
     * @return 平台经营者信息列表
     */
    public List<PlatformOperatorEmployeeInfo> selectPlatformOperatorEmployeeInfoByIdNumber(String idNumber);
}
