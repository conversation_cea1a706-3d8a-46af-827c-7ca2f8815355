package com.wendao101.teacher.service.impl;

import com.wendao101.teacher.domain.PlatformIncomeReport;
import com.wendao101.teacher.mapper.PlatformIncomeReportMapper;
import com.wendao101.teacher.service.IPlatformIncomeReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 平台内的经营者和从业人员收入信息报送表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class PlatformIncomeReportServiceImpl implements IPlatformIncomeReportService 
{
    @Autowired
    private PlatformIncomeReportMapper platformIncomeReportMapper;

    /**
     * 查询平台内的经营者和从业人员收入信息报送表
     * 
     * @param id 平台内的经营者和从业人员收入信息报送表主键
     * @return 平台内的经营者和从业人员收入信息报送表
     */
    @Override
    public PlatformIncomeReport selectPlatformIncomeReportById(Long id)
    {
        return platformIncomeReportMapper.selectPlatformIncomeReportById(id);
    }

    /**
     * 查询平台内的经营者和从业人员收入信息报送表列表
     * 
     * @param platformIncomeReport 平台内的经营者和从业人员收入信息报送表
     * @return 平台内的经营者和从业人员收入信息报送表
     */
    @Override
    public List<PlatformIncomeReport> selectPlatformIncomeReportList(PlatformIncomeReport platformIncomeReport)
    {
        return platformIncomeReportMapper.selectPlatformIncomeReportList(platformIncomeReport);
    }

    /**
     * 新增平台内的经营者和从业人员收入信息报送表
     * 
     * @param platformIncomeReport 平台内的经营者和从业人员收入信息报送表
     * @return 结果
     */
    @Override
    public int insertPlatformIncomeReport(PlatformIncomeReport platformIncomeReport)
    {
        return platformIncomeReportMapper.insertPlatformIncomeReport(platformIncomeReport);
    }

    /**
     * 修改平台内的经营者和从业人员收入信息报送表
     * 
     * @param platformIncomeReport 平台内的经营者和从业人员收入信息报送表
     * @return 结果
     */
    @Override
    public int updatePlatformIncomeReport(PlatformIncomeReport platformIncomeReport)
    {
        return platformIncomeReportMapper.updatePlatformIncomeReport(platformIncomeReport);
    }

    /**
     * 批量删除平台内的经营者和从业人员收入信息报送表
     * 
     * @param ids 需要删除的平台内的经营者和从业人员收入信息报送表主键
     * @return 结果
     */
    @Override
    public int deletePlatformIncomeReportByIds(Long[] ids)
    {
        return platformIncomeReportMapper.deletePlatformIncomeReportByIds(ids);
    }

    /**
     * 删除平台内的经营者和从业人员收入信息报送表信息
     * 
     * @param id 平台内的经营者和从业人员收入信息报送表主键
     * @return 结果
     */
    @Override
    public int deletePlatformIncomeReportById(Long id)
    {
        return platformIncomeReportMapper.deletePlatformIncomeReportById(id);
    }

    /**
     * 根据老师ID查询平台收入报告
     * 
     * @param teacherId 老师ID
     * @return 平台收入报告列表
     */
    @Override
    public List<PlatformIncomeReport> selectPlatformIncomeReportByTeacherId(Long teacherId)
    {
        return platformIncomeReportMapper.selectPlatformIncomeReportByTeacherId(teacherId);
    }

    /**
     * 根据平台名称和店铺ID查询平台收入报告
     * 
     * @param platformName 平台名称
     * @param shopId 店铺ID
     * @return 平台收入报告
     */
    @Override
    public PlatformIncomeReport selectPlatformIncomeReportByPlatformAndShop(String platformName, String shopId)
    {
        return platformIncomeReportMapper.selectPlatformIncomeReportByPlatformAndShop(platformName, shopId);
    }
}
