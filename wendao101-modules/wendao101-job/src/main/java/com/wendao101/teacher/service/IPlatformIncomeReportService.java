package com.wendao101.teacher.service;

import com.wendao101.teacher.domain.PlatformIncomeReport;

import java.util.List;

/**
 * 平台内的经营者和从业人员收入信息报送表Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IPlatformIncomeReportService 
{
    /**
     * 查询平台内的经营者和从业人员收入信息报送表
     * 
     * @param id 平台内的经营者和从业人员收入信息报送表主键
     * @return 平台内的经营者和从业人员收入信息报送表
     */
    public PlatformIncomeReport selectPlatformIncomeReportById(Long id);

    /**
     * 查询平台内的经营者和从业人员收入信息报送表列表
     * 
     * @param platformIncomeReport 平台内的经营者和从业人员收入信息报送表
     * @return 平台内的经营者和从业人员收入信息报送表集合
     */
    public List<PlatformIncomeReport> selectPlatformIncomeReportList(PlatformIncomeReport platformIncomeReport);

    /**
     * 新增平台内的经营者和从业人员收入信息报送表
     * 
     * @param platformIncomeReport 平台内的经营者和从业人员收入信息报送表
     * @return 结果
     */
    public int insertPlatformIncomeReport(PlatformIncomeReport platformIncomeReport);

    /**
     * 修改平台内的经营者和从业人员收入信息报送表
     * 
     * @param platformIncomeReport 平台内的经营者和从业人员收入信息报送表
     * @return 结果
     */
    public int updatePlatformIncomeReport(PlatformIncomeReport platformIncomeReport);

    /**
     * 批量删除平台内的经营者和从业人员收入信息报送表
     * 
     * @param ids 需要删除的平台内的经营者和从业人员收入信息报送表主键集合
     * @return 结果
     */
    public int deletePlatformIncomeReportByIds(Long[] ids);

    /**
     * 删除平台内的经营者和从业人员收入信息报送表信息
     * 
     * @param id 平台内的经营者和从业人员收入信息报送表主键
     * @return 结果
     */
    public int deletePlatformIncomeReportById(Long id);

    /**
     * 根据老师ID查询平台收入报告
     * 
     * @param teacherId 老师ID
     * @return 平台收入报告列表
     */
    public List<PlatformIncomeReport> selectPlatformIncomeReportByTeacherId(Long teacherId);

    /**
     * 根据平台名称和店铺ID查询平台收入报告
     * 
     * @param platformName 平台名称
     * @param shopId 店铺ID
     * @return 平台收入报告
     */
    public PlatformIncomeReport selectPlatformIncomeReportByPlatformAndShop(String platformName, String shopId);
}
