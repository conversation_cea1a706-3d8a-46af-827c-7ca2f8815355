package com.wendao101.teacher.domain;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 平台内的经营者和从业人员收入信息报送表对象 platform_income_report
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public class PlatformIncomeReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 平台名称 */
    @Excel(name = "平台名称")
    private String platformName;

    /** 序号 */
    @Excel(name = "序号")
    private Integer serialNumber;

    /** 是否已取得登记证照(Y/N) */
    @Excel(name = "是否已取得登记证照", readConverterExp = "Y=是,N=否")
    private String hasCertificate;

    /** 名称(姓名) */
    @Excel(name = "名称(姓名)")
    private String certificateName;

    /** 统一社会信用代码(纳税人识别号) */
    @Excel(name = "统一社会信用代码")
    private String certificateCode;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 证件类型 */
    @Excel(name = "证件类型")
    private String idType;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String idNumber;

    /** 国家或地区 */
    @Excel(name = "国家或地区")
    private String countryRegion;

    /** 收入来源的互联网平台名称 */
    @Excel(name = "收入来源的互联网平台名称")
    private String incomeSourcePlatform;

    /** 收入来源的店铺(用户)名称 */
    @Excel(name = "收入来源的店铺名称")
    private String incomeSourceShop;

    /** 收入来源的店铺(用户)唯一标识码 */
    @Excel(name = "收入来源的店铺唯一标识码")
    private String incomeSourceShopId;

    /** 销售货物取得的收入总额 */
    @Excel(name = "销售货物取得的收入总额")
    private BigDecimal goodsIncomeTotal;

    /** 销售货物退款金额 */
    @Excel(name = "销售货物退款金额")
    private BigDecimal goodsRefund;

    /** 销售无形资产取得的收入总额 */
    @Excel(name = "销售无形资产取得的收入总额")
    private BigDecimal intangibleIncomeTotal;

    /** 销售无形资产退款金额 */
    @Excel(name = "销售无形资产退款金额")
    private BigDecimal intangibleRefund;

    /** 销售服务取得的收入总额 */
    @Excel(name = "销售服务取得的收入总额")
    private BigDecimal serviceIncomeTotal;

    /** 销售服务退款金额 */
    @Excel(name = "销售服务退款金额")
    private BigDecimal serviceRefund;

    /** 收入总额 */
    @Excel(name = "收入总额")
    private BigDecimal totalIncome;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal totalRefund;

    /** 销售货物收入 */
    @Excel(name = "销售货物收入")
    private BigDecimal goodsIncome;

    /** 运输服务收入 */
    @Excel(name = "运输服务收入")
    private BigDecimal transportIncome;

    /** 劳务报酬收入 */
    @Excel(name = "劳务报酬收入")
    private BigDecimal laborIncome;

    /** 稿酬收入 */
    @Excel(name = "稿酬收入")
    private BigDecimal authorIncome;

    /** 特许权使用费收入 */
    @Excel(name = "特许权使用费收入")
    private BigDecimal royaltyIncome;

    /** 从事其他网络交易活动取得的收入(净额) */
    @Excel(name = "从事其他网络交易活动取得的收入")
    private BigDecimal otherIncomeNet;

    /** 支付给平台的佣金、服务费合计金额 */
    @Excel(name = "支付给平台的佣金、服务费合计金额")
    private BigDecimal platformCommission;

    /** 交易(订单)数量 */
    @Excel(name = "交易数量")
    private Long transactionCount;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 入驻的平台,1:微信,2:快手 */
    @Excel(name = "入驻的平台", readConverterExp = "1=微信,2=快手")
    private Integer orderPlatform;

    /** 来源的APP,1:问到好课,2:问到课堂 */
    @Excel(name = "来源的APP", readConverterExp = "1=问到好课,2=问到课堂")
    private Integer appNameType;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPlatformName(String platformName) 
    {
        this.platformName = platformName;
    }

    public String getPlatformName() 
    {
        return platformName;
    }
    public void setSerialNumber(Integer serialNumber) 
    {
        this.serialNumber = serialNumber;
    }

    public Integer getSerialNumber() 
    {
        return serialNumber;
    }
    public void setHasCertificate(String hasCertificate) 
    {
        this.hasCertificate = hasCertificate;
    }

    public String getHasCertificate() 
    {
        return hasCertificate;
    }
    public void setCertificateName(String certificateName) 
    {
        this.certificateName = certificateName;
    }

    public String getCertificateName() 
    {
        return certificateName;
    }
    public void setCertificateCode(String certificateCode) 
    {
        this.certificateCode = certificateCode;
    }

    public String getCertificateCode() 
    {
        return certificateCode;
    }
    public void setPersonName(String personName) 
    {
        this.personName = personName;
    }

    public String getPersonName() 
    {
        return personName;
    }
    public void setIdType(String idType) 
    {
        this.idType = idType;
    }

    public String getIdType() 
    {
        return idType;
    }
    public void setIdNumber(String idNumber) 
    {
        this.idNumber = idNumber;
    }

    public String getIdNumber() 
    {
        return idNumber;
    }
    public void setCountryRegion(String countryRegion) 
    {
        this.countryRegion = countryRegion;
    }

    public String getCountryRegion() 
    {
        return countryRegion;
    }
    public void setIncomeSourcePlatform(String incomeSourcePlatform) 
    {
        this.incomeSourcePlatform = incomeSourcePlatform;
    }

    public String getIncomeSourcePlatform() 
    {
        return incomeSourcePlatform;
    }
    public void setIncomeSourceShop(String incomeSourceShop) 
    {
        this.incomeSourceShop = incomeSourceShop;
    }

    public String getIncomeSourceShop() 
    {
        return incomeSourceShop;
    }
    public void setIncomeSourceShopId(String incomeSourceShopId) 
    {
        this.incomeSourceShopId = incomeSourceShopId;
    }

    public String getIncomeSourceShopId() 
    {
        return incomeSourceShopId;
    }
    public void setGoodsIncomeTotal(BigDecimal goodsIncomeTotal) 
    {
        this.goodsIncomeTotal = goodsIncomeTotal;
    }

    public BigDecimal getGoodsIncomeTotal() 
    {
        return goodsIncomeTotal;
    }
    public void setGoodsRefund(BigDecimal goodsRefund) 
    {
        this.goodsRefund = goodsRefund;
    }

    public BigDecimal getGoodsRefund() 
    {
        return goodsRefund;
    }
    public void setIntangibleIncomeTotal(BigDecimal intangibleIncomeTotal) 
    {
        this.intangibleIncomeTotal = intangibleIncomeTotal;
    }

    public BigDecimal getIntangibleIncomeTotal() 
    {
        return intangibleIncomeTotal;
    }
    public void setIntangibleRefund(BigDecimal intangibleRefund) 
    {
        this.intangibleRefund = intangibleRefund;
    }

    public BigDecimal getIntangibleRefund() 
    {
        return intangibleRefund;
    }
    public void setServiceIncomeTotal(BigDecimal serviceIncomeTotal) 
    {
        this.serviceIncomeTotal = serviceIncomeTotal;
    }

    public BigDecimal getServiceIncomeTotal() 
    {
        return serviceIncomeTotal;
    }
    public void setServiceRefund(BigDecimal serviceRefund) 
    {
        this.serviceRefund = serviceRefund;
    }

    public BigDecimal getServiceRefund() 
    {
        return serviceRefund;
    }
    public void setTotalIncome(BigDecimal totalIncome) 
    {
        this.totalIncome = totalIncome;
    }

    public BigDecimal getTotalIncome() 
    {
        return totalIncome;
    }
    public void setTotalRefund(BigDecimal totalRefund) 
    {
        this.totalRefund = totalRefund;
    }

    public BigDecimal getTotalRefund() 
    {
        return totalRefund;
    }
    public void setGoodsIncome(BigDecimal goodsIncome) 
    {
        this.goodsIncome = goodsIncome;
    }

    public BigDecimal getGoodsIncome() 
    {
        return goodsIncome;
    }
    public void setTransportIncome(BigDecimal transportIncome) 
    {
        this.transportIncome = transportIncome;
    }

    public BigDecimal getTransportIncome() 
    {
        return transportIncome;
    }
    public void setLaborIncome(BigDecimal laborIncome) 
    {
        this.laborIncome = laborIncome;
    }

    public BigDecimal getLaborIncome() 
    {
        return laborIncome;
    }
    public void setAuthorIncome(BigDecimal authorIncome) 
    {
        this.authorIncome = authorIncome;
    }

    public BigDecimal getAuthorIncome() 
    {
        return authorIncome;
    }
    public void setRoyaltyIncome(BigDecimal royaltyIncome) 
    {
        this.royaltyIncome = royaltyIncome;
    }

    public BigDecimal getRoyaltyIncome() 
    {
        return royaltyIncome;
    }
    public void setOtherIncomeNet(BigDecimal otherIncomeNet) 
    {
        this.otherIncomeNet = otherIncomeNet;
    }

    public BigDecimal getOtherIncomeNet() 
    {
        return otherIncomeNet;
    }
    public void setPlatformCommission(BigDecimal platformCommission) 
    {
        this.platformCommission = platformCommission;
    }

    public BigDecimal getPlatformCommission() 
    {
        return platformCommission;
    }
    public void setTransactionCount(Long transactionCount) 
    {
        this.transactionCount = transactionCount;
    }

    public Long getTransactionCount() 
    {
        return transactionCount;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setOrderPlatform(Integer orderPlatform) 
    {
        this.orderPlatform = orderPlatform;
    }

    public Integer getOrderPlatform() 
    {
        return orderPlatform;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("platformName", getPlatformName())
            .append("serialNumber", getSerialNumber())
            .append("hasCertificate", getHasCertificate())
            .append("certificateName", getCertificateName())
            .append("certificateCode", getCertificateCode())
            .append("personName", getPersonName())
            .append("idType", getIdType())
            .append("idNumber", getIdNumber())
            .append("countryRegion", getCountryRegion())
            .append("incomeSourcePlatform", getIncomeSourcePlatform())
            .append("incomeSourceShop", getIncomeSourceShop())
            .append("incomeSourceShopId", getIncomeSourceShopId())
            .append("goodsIncomeTotal", getGoodsIncomeTotal())
            .append("goodsRefund", getGoodsRefund())
            .append("intangibleIncomeTotal", getIntangibleIncomeTotal())
            .append("intangibleRefund", getIntangibleRefund())
            .append("serviceIncomeTotal", getServiceIncomeTotal())
            .append("serviceRefund", getServiceRefund())
            .append("totalIncome", getTotalIncome())
            .append("totalRefund", getTotalRefund())
            .append("goodsIncome", getGoodsIncome())
            .append("transportIncome", getTransportIncome())
            .append("laborIncome", getLaborIncome())
            .append("authorIncome", getAuthorIncome())
            .append("royaltyIncome", getRoyaltyIncome())
            .append("otherIncomeNet", getOtherIncomeNet())
            .append("platformCommission", getPlatformCommission())
            .append("transactionCount", getTransactionCount())
            .append("teacherId", getTeacherId())
            .append("orderPlatform", getOrderPlatform())
            .append("appNameType", getAppNameType())
            .toString();
    }
}
