package com.wendao101.teacher.service.impl;

import com.wendao101.teacher.domain.PlatformOperatorEmployeeInfo;
import com.wendao101.teacher.mapper.PlatformOperatorEmployeeInfoMapper;
import com.wendao101.teacher.service.IPlatformOperatorEmployeeInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 平台经营者和从业人员身份信息报送表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class PlatformOperatorEmployeeInfoServiceImpl implements IPlatformOperatorEmployeeInfoService 
{
    @Autowired
    private PlatformOperatorEmployeeInfoMapper platformOperatorEmployeeInfoMapper;

    /**
     * 查询平台经营者和从业人员身份信息报送表
     * 
     * @param id 平台经营者和从业人员身份信息报送表主键
     * @return 平台经营者和从业人员身份信息报送表
     */
    @Override
    public PlatformOperatorEmployeeInfo selectPlatformOperatorEmployeeInfoById(Long id)
    {
        return platformOperatorEmployeeInfoMapper.selectPlatformOperatorEmployeeInfoById(id);
    }

    /**
     * 查询平台经营者和从业人员身份信息报送表列表
     * 
     * @param platformOperatorEmployeeInfo 平台经营者和从业人员身份信息报送表
     * @return 平台经营者和从业人员身份信息报送表
     */
    @Override
    public List<PlatformOperatorEmployeeInfo> selectPlatformOperatorEmployeeInfoList(PlatformOperatorEmployeeInfo platformOperatorEmployeeInfo)
    {
        return platformOperatorEmployeeInfoMapper.selectPlatformOperatorEmployeeInfoList(platformOperatorEmployeeInfo);
    }

    /**
     * 新增平台经营者和从业人员身份信息报送表
     * 
     * @param platformOperatorEmployeeInfo 平台经营者和从业人员身份信息报送表
     * @return 结果
     */
    @Override
    public int insertPlatformOperatorEmployeeInfo(PlatformOperatorEmployeeInfo platformOperatorEmployeeInfo)
    {
        return platformOperatorEmployeeInfoMapper.insertPlatformOperatorEmployeeInfo(platformOperatorEmployeeInfo);
    }

    /**
     * 修改平台经营者和从业人员身份信息报送表
     * 
     * @param platformOperatorEmployeeInfo 平台经营者和从业人员身份信息报送表
     * @return 结果
     */
    @Override
    public int updatePlatformOperatorEmployeeInfo(PlatformOperatorEmployeeInfo platformOperatorEmployeeInfo)
    {
        return platformOperatorEmployeeInfoMapper.updatePlatformOperatorEmployeeInfo(platformOperatorEmployeeInfo);
    }

    /**
     * 批量删除平台经营者和从业人员身份信息报送表
     * 
     * @param ids 需要删除的平台经营者和从业人员身份信息报送表主键
     * @return 结果
     */
    @Override
    public int deletePlatformOperatorEmployeeInfoByIds(Long[] ids)
    {
        return platformOperatorEmployeeInfoMapper.deletePlatformOperatorEmployeeInfoByIds(ids);
    }

    /**
     * 删除平台经营者和从业人员身份信息报送表信息
     * 
     * @param id 平台经营者和从业人员身份信息报送表主键
     * @return 结果
     */
    @Override
    public int deletePlatformOperatorEmployeeInfoById(Long id)
    {
        return platformOperatorEmployeeInfoMapper.deletePlatformOperatorEmployeeInfoById(id);
    }

    /**
     * 根据老师ID查询平台经营者信息
     * 
     * @param teacherId 老师ID
     * @return 平台经营者信息列表
     */
    @Override
    public List<PlatformOperatorEmployeeInfo> selectPlatformOperatorEmployeeInfoByTeacherId(Long teacherId)
    {
        return platformOperatorEmployeeInfoMapper.selectPlatformOperatorEmployeeInfoByTeacherId(teacherId);
    }

    /**
     * 根据店铺唯一标识码查询平台经营者信息
     * 
     * @param shopUniqueId 店铺唯一标识码
     * @return 平台经营者信息
     */
    @Override
    public PlatformOperatorEmployeeInfo selectPlatformOperatorEmployeeInfoByShopId(String shopUniqueId)
    {
        return platformOperatorEmployeeInfoMapper.selectPlatformOperatorEmployeeInfoByShopId(shopUniqueId);
    }

    /**
     * 根据证件号码查询平台经营者信息
     * 
     * @param idNumber 证件号码
     * @return 平台经营者信息列表
     */
    @Override
    public List<PlatformOperatorEmployeeInfo> selectPlatformOperatorEmployeeInfoByIdNumber(String idNumber)
    {
        return platformOperatorEmployeeInfoMapper.selectPlatformOperatorEmployeeInfoByIdNumber(idNumber);
    }
}
