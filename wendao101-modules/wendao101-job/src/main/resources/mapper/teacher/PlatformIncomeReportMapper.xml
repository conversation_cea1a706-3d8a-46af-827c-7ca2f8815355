<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.PlatformIncomeReportMapper">
    
    <resultMap type="PlatformIncomeReport" id="PlatformIncomeReportResult">
        <result property="id"    column="id"    />
        <result property="platformName"    column="platform_name"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="hasCertificate"    column="has_certificate"    />
        <result property="certificateName"    column="certificate_name"    />
        <result property="certificateCode"    column="certificate_code"    />
        <result property="personName"    column="person_name"    />
        <result property="idType"    column="id_type"    />
        <result property="idNumber"    column="id_number"    />
        <result property="countryRegion"    column="country_region"    />
        <result property="incomeSourcePlatform"    column="income_source_platform"    />
        <result property="incomeSourceShop"    column="income_source_shop"    />
        <result property="incomeSourceShopId"    column="income_source_shop_id"    />
        <result property="goodsIncomeTotal"    column="goods_income_total"    />
        <result property="goodsRefund"    column="goods_refund"    />
        <result property="intangibleIncomeTotal"    column="intangible_income_total"    />
        <result property="intangibleRefund"    column="intangible_refund"    />
        <result property="serviceIncomeTotal"    column="service_income_total"    />
        <result property="serviceRefund"    column="service_refund"    />
        <result property="totalIncome"    column="total_income"    />
        <result property="totalRefund"    column="total_refund"    />
        <result property="goodsIncome"    column="goods_income"    />
        <result property="transportIncome"    column="transport_income"    />
        <result property="laborIncome"    column="labor_income"    />
        <result property="authorIncome"    column="author_income"    />
        <result property="royaltyIncome"    column="royalty_income"    />
        <result property="otherIncomeNet"    column="other_income_net"    />
        <result property="platformCommission"    column="platform_commission"    />
        <result property="transactionCount"    column="transaction_count"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="orderPlatform"    column="order_platform"    />
        <result property="appNameType"    column="app_name_type"    />
    </resultMap>

    <sql id="selectPlatformIncomeReportVo">
        select id, platform_name, serial_number, has_certificate, certificate_name, certificate_code, person_name, id_type, id_number, country_region, income_source_platform, income_source_shop, income_source_shop_id, goods_income_total, goods_refund, intangible_income_total, intangible_refund, service_income_total, service_refund, total_income, total_refund, goods_income, transport_income, labor_income, author_income, royalty_income, other_income_net, platform_commission, transaction_count, teacher_id, order_platform, app_name_type from `wendao101-order`.platform_income_report
    </sql>

    <select id="selectPlatformIncomeReportList" parameterType="PlatformIncomeReport" resultMap="PlatformIncomeReportResult">
        <include refid="selectPlatformIncomeReportVo"/>
        <where>  
            <if test="platformName != null  and platformName != ''"> and platform_name like concat('%', #{platformName}, '%')</if>
            <if test="serialNumber != null "> and serial_number = #{serialNumber}</if>
            <if test="hasCertificate != null  and hasCertificate != ''"> and has_certificate = #{hasCertificate}</if>
            <if test="certificateName != null  and certificateName != ''"> and certificate_name like concat('%', #{certificateName}, '%')</if>
            <if test="certificateCode != null  and certificateCode != ''"> and certificate_code = #{certificateCode}</if>
            <if test="personName != null  and personName != ''"> and person_name like concat('%', #{personName}, '%')</if>
            <if test="idType != null  and idType != ''"> and id_type = #{idType}</if>
            <if test="idNumber != null  and idNumber != ''"> and id_number = #{idNumber}</if>
            <if test="countryRegion != null  and countryRegion != ''"> and country_region like concat('%', #{countryRegion}, '%')</if>
            <if test="incomeSourcePlatform != null  and incomeSourcePlatform != ''"> and income_source_platform like concat('%', #{incomeSourcePlatform}, '%')</if>
            <if test="incomeSourceShop != null  and incomeSourceShop != ''"> and income_source_shop like concat('%', #{incomeSourceShop}, '%')</if>
            <if test="incomeSourceShopId != null  and incomeSourceShopId != ''"> and income_source_shop_id = #{incomeSourceShopId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="orderPlatform != null "> and order_platform = #{orderPlatform}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
    </select>
    
    <select id="selectPlatformIncomeReportById" parameterType="Long" resultMap="PlatformIncomeReportResult">
        <include refid="selectPlatformIncomeReportVo"/>
        where id = #{id}
    </select>

    <select id="selectPlatformIncomeReportByTeacherId" parameterType="Long" resultMap="PlatformIncomeReportResult">
        <include refid="selectPlatformIncomeReportVo"/>
        where teacher_id = #{teacherId}
    </select>

    <select id="selectPlatformIncomeReportByPlatformAndShop" resultMap="PlatformIncomeReportResult">
        <include refid="selectPlatformIncomeReportVo"/>
        where income_source_platform = #{platformName} and income_source_shop_id = #{shopId}
    </select>
        
    <insert id="insertPlatformIncomeReport" parameterType="PlatformIncomeReport" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.platform_income_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platformName != null and platformName != ''">platform_name,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="hasCertificate != null and hasCertificate != ''">has_certificate,</if>
            <if test="certificateName != null">certificate_name,</if>
            <if test="certificateCode != null">certificate_code,</if>
            <if test="personName != null">person_name,</if>
            <if test="idType != null">id_type,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="countryRegion != null">country_region,</if>
            <if test="incomeSourcePlatform != null and incomeSourcePlatform != ''">income_source_platform,</if>
            <if test="incomeSourceShop != null and incomeSourceShop != ''">income_source_shop,</if>
            <if test="incomeSourceShopId != null and incomeSourceShopId != ''">income_source_shop_id,</if>
            <if test="goodsIncomeTotal != null">goods_income_total,</if>
            <if test="goodsRefund != null">goods_refund,</if>
            <if test="intangibleIncomeTotal != null">intangible_income_total,</if>
            <if test="intangibleRefund != null">intangible_refund,</if>
            <if test="serviceIncomeTotal != null">service_income_total,</if>
            <if test="serviceRefund != null">service_refund,</if>
            <if test="totalIncome != null">total_income,</if>
            <if test="totalRefund != null">total_refund,</if>
            <if test="goodsIncome != null">goods_income,</if>
            <if test="transportIncome != null">transport_income,</if>
            <if test="laborIncome != null">labor_income,</if>
            <if test="authorIncome != null">author_income,</if>
            <if test="royaltyIncome != null">royalty_income,</if>
            <if test="otherIncomeNet != null">other_income_net,</if>
            <if test="platformCommission != null">platform_commission,</if>
            <if test="transactionCount != null">transaction_count,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="orderPlatform != null">order_platform,</if>
            <if test="appNameType != null">app_name_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platformName != null and platformName != ''">#{platformName},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="hasCertificate != null and hasCertificate != ''">#{hasCertificate},</if>
            <if test="certificateName != null">#{certificateName},</if>
            <if test="certificateCode != null">#{certificateCode},</if>
            <if test="personName != null">#{personName},</if>
            <if test="idType != null">#{idType},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="countryRegion != null">#{countryRegion},</if>
            <if test="incomeSourcePlatform != null and incomeSourcePlatform != ''">#{incomeSourcePlatform},</if>
            <if test="incomeSourceShop != null and incomeSourceShop != ''">#{incomeSourceShop},</if>
            <if test="incomeSourceShopId != null and incomeSourceShopId != ''">#{incomeSourceShopId},</if>
            <if test="goodsIncomeTotal != null">#{goodsIncomeTotal},</if>
            <if test="goodsRefund != null">#{goodsRefund},</if>
            <if test="intangibleIncomeTotal != null">#{intangibleIncomeTotal},</if>
            <if test="intangibleRefund != null">#{intangibleRefund},</if>
            <if test="serviceIncomeTotal != null">#{serviceIncomeTotal},</if>
            <if test="serviceRefund != null">#{serviceRefund},</if>
            <if test="totalIncome != null">#{totalIncome},</if>
            <if test="totalRefund != null">#{totalRefund},</if>
            <if test="goodsIncome != null">#{goodsIncome},</if>
            <if test="transportIncome != null">#{transportIncome},</if>
            <if test="laborIncome != null">#{laborIncome},</if>
            <if test="authorIncome != null">#{authorIncome},</if>
            <if test="royaltyIncome != null">#{royaltyIncome},</if>
            <if test="otherIncomeNet != null">#{otherIncomeNet},</if>
            <if test="platformCommission != null">#{platformCommission},</if>
            <if test="transactionCount != null">#{transactionCount},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="orderPlatform != null">#{orderPlatform},</if>
            <if test="appNameType != null">#{appNameType},</if>
         </trim>
    </insert>

    <update id="updatePlatformIncomeReport" parameterType="PlatformIncomeReport">
        update `wendao101-order`.platform_income_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="platformName != null and platformName != ''">platform_name = #{platformName},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="hasCertificate != null and hasCertificate != ''">has_certificate = #{hasCertificate},</if>
            <if test="certificateName != null">certificate_name = #{certificateName},</if>
            <if test="certificateCode != null">certificate_code = #{certificateCode},</if>
            <if test="personName != null">person_name = #{personName},</if>
            <if test="idType != null">id_type = #{idType},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="countryRegion != null">country_region = #{countryRegion},</if>
            <if test="incomeSourcePlatform != null and incomeSourcePlatform != ''">income_source_platform = #{incomeSourcePlatform},</if>
            <if test="incomeSourceShop != null and incomeSourceShop != ''">income_source_shop = #{incomeSourceShop},</if>
            <if test="incomeSourceShopId != null and incomeSourceShopId != ''">income_source_shop_id = #{incomeSourceShopId},</if>
            <if test="goodsIncomeTotal != null">goods_income_total = #{goodsIncomeTotal},</if>
            <if test="goodsRefund != null">goods_refund = #{goodsRefund},</if>
            <if test="intangibleIncomeTotal != null">intangible_income_total = #{intangibleIncomeTotal},</if>
            <if test="intangibleRefund != null">intangible_refund = #{intangibleRefund},</if>
            <if test="serviceIncomeTotal != null">service_income_total = #{serviceIncomeTotal},</if>
            <if test="serviceRefund != null">service_refund = #{serviceRefund},</if>
            <if test="totalIncome != null">total_income = #{totalIncome},</if>
            <if test="totalRefund != null">total_refund = #{totalRefund},</if>
            <if test="goodsIncome != null">goods_income = #{goodsIncome},</if>
            <if test="transportIncome != null">transport_income = #{transportIncome},</if>
            <if test="laborIncome != null">labor_income = #{laborIncome},</if>
            <if test="authorIncome != null">author_income = #{authorIncome},</if>
            <if test="royaltyIncome != null">royalty_income = #{royaltyIncome},</if>
            <if test="otherIncomeNet != null">other_income_net = #{otherIncomeNet},</if>
            <if test="platformCommission != null">platform_commission = #{platformCommission},</if>
            <if test="transactionCount != null">transaction_count = #{transactionCount},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="orderPlatform != null">order_platform = #{orderPlatform},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlatformIncomeReportById" parameterType="Long">
        delete from `wendao101-order`.platform_income_report where id = #{id}
    </delete>

    <delete id="deletePlatformIncomeReportByIds" parameterType="String">
        delete from `wendao101-order`.platform_income_report where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
