<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.PlatformOperatorEmployeeInfoMapper">
    
    <resultMap type="PlatformOperatorEmployeeInfo" id="PlatformOperatorEmployeeInfoResult">
        <result property="id"    column="id"    />
        <result property="platformName"    column="platform_name"    />
        <result property="hasCertificate"    column="has_certificate"    />
        <result property="registeredName"    column="registered_name"    />
        <result property="registeredCreditCode"    column="registered_credit_code"    />
        <result property="professionalServiceAgency"    column="professional_service_agency"    />
        <result property="personalName"    column="personal_name"    />
        <result property="idType"    column="id_type"    />
        <result property="idNumber"    column="id_number"    />
        <result property="countryRegion"    column="country_region"    />
        <result property="isIncomeExempt"    column="is_income_exempt"    />
        <result property="exemptionType"    column="exemption_type"    />
        <result property="address"    column="address"    />
        <result property="shopName"    column="shop_name"    />
        <result property="shopUniqueId"    column="shop_unique_id"    />
        <result property="shopUrl"    column="shop_url"    />
        <result property="settlementAccount"    column="settlement_account"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="businessStartDate"    column="business_start_date"    />
        <result property="businessEndDate"    column="business_end_date"    />
        <result property="infoStatus"    column="info_status"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="orderPlatform"    column="order_platform"    />
        <result property="appNameType"    column="app_name_type"    />
    </resultMap>

    <sql id="selectPlatformOperatorEmployeeInfoVo">
        select id, platform_name, has_certificate, registered_name, registered_credit_code, professional_service_agency, personal_name, id_type, id_number, country_region, is_income_exempt, exemption_type, address, shop_name, shop_unique_id, shop_url, settlement_account, contact_name, contact_phone, business_start_date, business_end_date, info_status, created_at, updated_at, teacher_id, order_platform, app_name_type from `wendao101-order`.platform_operator_employee_info
    </sql>

    <select id="selectPlatformOperatorEmployeeInfoList" parameterType="PlatformOperatorEmployeeInfo" resultMap="PlatformOperatorEmployeeInfoResult">
        <include refid="selectPlatformOperatorEmployeeInfoVo"/>
        <where>  
            <if test="platformName != null  and platformName != ''"> and platform_name like concat('%', #{platformName}, '%')</if>
            <if test="hasCertificate != null  and hasCertificate != ''"> and has_certificate = #{hasCertificate}</if>
            <if test="registeredName != null  and registeredName != ''"> and registered_name like concat('%', #{registeredName}, '%')</if>
            <if test="registeredCreditCode != null  and registeredCreditCode != ''"> and registered_credit_code = #{registeredCreditCode}</if>
            <if test="professionalServiceAgency != null  and professionalServiceAgency != ''"> and professional_service_agency = #{professionalServiceAgency}</if>
            <if test="personalName != null  and personalName != ''"> and personal_name like concat('%', #{personalName}, '%')</if>
            <if test="idType != null  and idType != ''"> and id_type = #{idType}</if>
            <if test="idNumber != null  and idNumber != ''"> and id_number = #{idNumber}</if>
            <if test="countryRegion != null  and countryRegion != ''"> and country_region = #{countryRegion}</if>
            <if test="isIncomeExempt != null  and isIncomeExempt != ''"> and is_income_exempt = #{isIncomeExempt}</if>
            <if test="exemptionType != null  and exemptionType != ''"> and exemption_type = #{exemptionType}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="shopUniqueId != null  and shopUniqueId != ''"> and shop_unique_id = #{shopUniqueId}</if>
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="businessStartDate != null "> and business_start_date = #{businessStartDate}</if>
            <if test="businessEndDate != null "> and business_end_date = #{businessEndDate}</if>
            <if test="infoStatus != null  and infoStatus != ''"> and info_status = #{infoStatus}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="orderPlatform != null "> and order_platform = #{orderPlatform}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
    </select>
    
    <select id="selectPlatformOperatorEmployeeInfoById" parameterType="Long" resultMap="PlatformOperatorEmployeeInfoResult">
        <include refid="selectPlatformOperatorEmployeeInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectPlatformOperatorEmployeeInfoByTeacherId" parameterType="Long" resultMap="PlatformOperatorEmployeeInfoResult">
        <include refid="selectPlatformOperatorEmployeeInfoVo"/>
        where teacher_id = #{teacherId}
    </select>

    <select id="selectPlatformOperatorEmployeeInfoByShopId" parameterType="String" resultMap="PlatformOperatorEmployeeInfoResult">
        <include refid="selectPlatformOperatorEmployeeInfoVo"/>
        where shop_unique_id = #{shopUniqueId}
    </select>

    <select id="selectPlatformOperatorEmployeeInfoByIdNumber" parameterType="String" resultMap="PlatformOperatorEmployeeInfoResult">
        <include refid="selectPlatformOperatorEmployeeInfoVo"/>
        where id_number = #{idNumber}
    </select>
        
    <insert id="insertPlatformOperatorEmployeeInfo" parameterType="PlatformOperatorEmployeeInfo" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.platform_operator_employee_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platformName != null and platformName != ''">platform_name,</if>
            <if test="hasCertificate != null and hasCertificate != ''">has_certificate,</if>
            <if test="registeredName != null">registered_name,</if>
            <if test="registeredCreditCode != null">registered_credit_code,</if>
            <if test="professionalServiceAgency != null">professional_service_agency,</if>
            <if test="personalName != null">personal_name,</if>
            <if test="idType != null">id_type,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="countryRegion != null">country_region,</if>
            <if test="isIncomeExempt != null">is_income_exempt,</if>
            <if test="exemptionType != null">exemption_type,</if>
            <if test="address != null">address,</if>
            <if test="shopName != null and shopName != ''">shop_name,</if>
            <if test="shopUniqueId != null and shopUniqueId != ''">shop_unique_id,</if>
            <if test="shopUrl != null">shop_url,</if>
            <if test="settlementAccount != null">settlement_account,</if>
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="businessStartDate != null">business_start_date,</if>
            <if test="businessEndDate != null">business_end_date,</if>
            <if test="infoStatus != null and infoStatus != ''">info_status,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="orderPlatform != null">order_platform,</if>
            <if test="appNameType != null">app_name_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platformName != null and platformName != ''">#{platformName},</if>
            <if test="hasCertificate != null and hasCertificate != ''">#{hasCertificate},</if>
            <if test="registeredName != null">#{registeredName},</if>
            <if test="registeredCreditCode != null">#{registeredCreditCode},</if>
            <if test="professionalServiceAgency != null">#{professionalServiceAgency},</if>
            <if test="personalName != null">#{personalName},</if>
            <if test="idType != null">#{idType},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="countryRegion != null">#{countryRegion},</if>
            <if test="isIncomeExempt != null">#{isIncomeExempt},</if>
            <if test="exemptionType != null">#{exemptionType},</if>
            <if test="address != null">#{address},</if>
            <if test="shopName != null and shopName != ''">#{shopName},</if>
            <if test="shopUniqueId != null and shopUniqueId != ''">#{shopUniqueId},</if>
            <if test="shopUrl != null">#{shopUrl},</if>
            <if test="settlementAccount != null">#{settlementAccount},</if>
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="businessStartDate != null">#{businessStartDate},</if>
            <if test="businessEndDate != null">#{businessEndDate},</if>
            <if test="infoStatus != null and infoStatus != ''">#{infoStatus},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="orderPlatform != null">#{orderPlatform},</if>
            <if test="appNameType != null">#{appNameType},</if>
         </trim>
    </insert>

    <update id="updatePlatformOperatorEmployeeInfo" parameterType="PlatformOperatorEmployeeInfo">
        update `wendao101-order`.platform_operator_employee_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="platformName != null and platformName != ''">platform_name = #{platformName},</if>
            <if test="hasCertificate != null and hasCertificate != ''">has_certificate = #{hasCertificate},</if>
            <if test="registeredName != null">registered_name = #{registeredName},</if>
            <if test="registeredCreditCode != null">registered_credit_code = #{registeredCreditCode},</if>
            <if test="professionalServiceAgency != null">professional_service_agency = #{professionalServiceAgency},</if>
            <if test="personalName != null">personal_name = #{personalName},</if>
            <if test="idType != null">id_type = #{idType},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="countryRegion != null">country_region = #{countryRegion},</if>
            <if test="isIncomeExempt != null">is_income_exempt = #{isIncomeExempt},</if>
            <if test="exemptionType != null">exemption_type = #{exemptionType},</if>
            <if test="address != null">address = #{address},</if>
            <if test="shopName != null and shopName != ''">shop_name = #{shopName},</if>
            <if test="shopUniqueId != null and shopUniqueId != ''">shop_unique_id = #{shopUniqueId},</if>
            <if test="shopUrl != null">shop_url = #{shopUrl},</if>
            <if test="settlementAccount != null">settlement_account = #{settlementAccount},</if>
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="businessStartDate != null">business_start_date = #{businessStartDate},</if>
            <if test="businessEndDate != null">business_end_date = #{businessEndDate},</if>
            <if test="infoStatus != null and infoStatus != ''">info_status = #{infoStatus},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="orderPlatform != null">order_platform = #{orderPlatform},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlatformOperatorEmployeeInfoById" parameterType="Long">
        delete from `wendao101-order`.platform_operator_employee_info where id = #{id}
    </delete>

    <delete id="deletePlatformOperatorEmployeeInfoByIds" parameterType="String">
        delete from `wendao101-order`.platform_operator_employee_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
